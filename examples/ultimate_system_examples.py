#!/usr/bin/env python3
"""
Ultimate Professional Reaction System Examples
Comprehensive examples demonstrating the pinnacle of computational chemistry analysis.
This showcases the ultimate integration of all advanced features.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def example_pharmaceutical_synthesis():
    """Example: Complete pharmaceutical intermediate synthesis analysis."""
    print("\n" + "="*80)
    print("🏥 PHARMACEUTICAL SYNTHESIS - ULTIMATE ANALYSIS")
    print("="*80)
    print("Objective: Synthesize acetaminophen precursor with optimal conditions")
    
    from ultimate_reaction_system import UltimateProfessionalReactionSystem, UltimateAnalysisRequest
    
    # Initialize ultimate system
    ultimate_system = UltimateProfessionalReactionSystem(dft_level='b3lyp/6-31g*')
    
    # Define pharmaceutical synthesis
    reactants = ['CC(=O)O', 'Nc1ccc(O)cc1']  # Acetic acid + p-aminophenol
    target_products = ['CC(=O)Nc1ccc(O)cc1']  # Acetaminophen
    
    # Create comprehensive analysis request
    request = UltimateAnalysisRequest(
        reactant_smiles=reactants,
        target_products=target_products,
        include_product_prediction=True,
        include_pathway_analysis=True,
        include_side_reactions=True,
        include_catalyst_design=True,
        include_solvent_optimization=True,
        include_condition_optimization=True,
        include_ts_search=False,  # Skip for example speed
        optimization_objectives=['yield', 'selectivity', 'safety'],
        safety_constraints={'max_temperature': 400, 'max_pressure': 5},
        environmental_constraints={'green_chemistry': True},
        cost_constraints={'max_cost': 'moderate'}
    )
    
    # Perform ultimate analysis
    result = ultimate_system.analyze_reaction_ultimate(request)
    
    # Export comprehensive results
    ultimate_system.export_ultimate_results(result, 'pharmaceutical_ultimate_analysis.json')
    
    print("\n✅ Pharmaceutical synthesis analysis completed!")
    return result

def example_green_chemistry_optimization():
    """Example: Green chemistry optimization with environmental constraints."""
    print("\n" + "="*80)
    print("🌱 GREEN CHEMISTRY OPTIMIZATION - ULTIMATE ANALYSIS")
    print("="*80)
    print("Objective: Develop environmentally friendly esterification process")
    
    from ultimate_reaction_system import UltimateProfessionalReactionSystem, UltimateAnalysisRequest
    
    # Initialize system
    ultimate_system = UltimateProfessionalReactionSystem(dft_level='b3lyp/6-31g*')
    
    # Green esterification
    reactants = ['CCO', 'CC(=O)O']  # Ethanol + Acetic acid
    target_products = ['CC(=O)OCC']  # Ethyl acetate
    
    # Green chemistry focused request
    request = UltimateAnalysisRequest(
        reactant_smiles=reactants,
        target_products=target_products,
        optimization_objectives=['yield', 'selectivity', 'safety'],
        environmental_constraints={
            'green_chemistry': True,
            'renewable_feedstock': True,
            'minimal_waste': True
        },
        safety_constraints={
            'max_toxicity': 'low',
            'max_temperature': 350
        },
        cost_constraints={
            'max_cost': 'low'
        }
    )
    
    # Perform analysis
    result = ultimate_system.analyze_reaction_ultimate(request)
    
    # Display green chemistry metrics
    if result.executive_summary:
        print(f"\n🌱 GREEN CHEMISTRY ASSESSMENT")
        print(f"Environmental impact: {result.risk_assessment.get('environmental_risk', {}).get('level', 'unknown')}")
        print(f"Safety profile: {result.risk_assessment.get('safety_risk', {}).get('level', 'unknown')}")
    
    print("\n✅ Green chemistry optimization completed!")
    return result

def example_industrial_scale_optimization():
    """Example: Industrial scale process optimization."""
    print("\n" + "="*80)
    print("🏭 INDUSTRIAL SCALE OPTIMIZATION - ULTIMATE ANALYSIS")
    print("="*80)
    print("Objective: Optimize large-scale chemical process for maximum efficiency")
    
    from ultimate_reaction_system import UltimateProfessionalReactionSystem, UltimateAnalysisRequest
    
    # Initialize system
    ultimate_system = UltimateProfessionalReactionSystem(dft_level='b3lyp/6-31g*')
    
    # Industrial Suzuki coupling
    reactants = ['c1ccc(Br)cc1', 'c1ccc(B(O)O)cc1']  # Bromobenzene + Phenylboronic acid
    target_products = ['c1ccc(-c2ccccc2)cc1']  # Biphenyl
    
    # Industrial optimization request
    request = UltimateAnalysisRequest(
        reactant_smiles=reactants,
        target_products=target_products,
        optimization_objectives=['yield', 'cost', 'safety'],
        cost_constraints={
            'max_cost': 'moderate',
            'catalyst_recovery': True
        },
        safety_constraints={
            'industrial_safety': True,
            'max_pressure': 10
        }
    )
    
    # Perform analysis
    result = ultimate_system.analyze_reaction_ultimate(request)
    
    # Display industrial metrics
    if result.economic_analysis:
        print(f"\n🏭 INDUSTRIAL ASSESSMENT")
        print(f"Economic viability: {result.economic_analysis.get('economic_viability', 'unknown')}")
        print(f"Cost estimate: {result.economic_analysis.get('cost_estimate', 'unknown')}")
        print(f"Key cost drivers: {', '.join(result.economic_analysis.get('cost_drivers', []))}")
    
    print("\n✅ Industrial scale optimization completed!")
    return result

def example_catalyst_screening_study():
    """Example: Comprehensive catalyst screening study."""
    print("\n" + "="*80)
    print("🧪 CATALYST SCREENING STUDY - ULTIMATE ANALYSIS")
    print("="*80)
    print("Objective: Find optimal catalyst for asymmetric hydrogenation")
    
    from ultimate_reaction_system import UltimateProfessionalReactionSystem, UltimateAnalysisRequest
    
    # Initialize system
    ultimate_system = UltimateProfessionalReactionSystem(dft_level='b3lyp/6-31g*')
    
    # Asymmetric hydrogenation
    reactants = ['CC(=O)C=C', '[H][H]']  # Enone + Hydrogen
    target_products = ['CC(=O)CC']  # Reduced product
    
    # Catalyst-focused request
    request = UltimateAnalysisRequest(
        reactant_smiles=reactants,
        target_products=target_products,
        include_catalyst_design=True,
        include_condition_optimization=True,
        optimization_objectives=['yield', 'selectivity'],
        cost_constraints={
            'catalyst_cost': 'high_acceptable'  # Accept expensive catalysts for selectivity
        }
    )
    
    # Perform analysis
    result = ultimate_system.analyze_reaction_ultimate(request)
    
    # Display catalyst recommendations
    if result.catalyst_recommendations and result.catalyst_recommendations.top_catalysts:
        print(f"\n🧪 CATALYST RECOMMENDATIONS")
        for i, cat in enumerate(result.catalyst_recommendations.top_catalysts[:3], 1):
            print(f"{i}. {cat.catalyst_name}")
            print(f"   Type: {cat.catalyst_type}")
            print(f"   Confidence: {cat.confidence:.2f}")
            print(f"   Cost: {cat.cost_estimate}")
    
    print("\n✅ Catalyst screening study completed!")
    return result

def example_multi_objective_optimization():
    """Example: Complex multi-objective optimization."""
    print("\n" + "="*80)
    print("🎯 MULTI-OBJECTIVE OPTIMIZATION - ULTIMATE ANALYSIS")
    print("="*80)
    print("Objective: Balance yield, selectivity, cost, and environmental impact")
    
    from ultimate_reaction_system import UltimateProfessionalReactionSystem, UltimateAnalysisRequest
    
    # Initialize system
    ultimate_system = UltimateProfessionalReactionSystem(dft_level='b3lyp/6-31g*')
    
    # Complex organic synthesis
    reactants = ['CC(=O)Cl', 'c1ccccc1N']  # Acetyl chloride + Aniline
    target_products = ['CC(=O)Nc1ccccc1']  # Acetanilide
    
    # Multi-objective request
    request = UltimateAnalysisRequest(
        reactant_smiles=reactants,
        target_products=target_products,
        optimization_objectives=['yield', 'selectivity', 'cost', 'safety'],
        include_product_prediction=True,
        include_pathway_analysis=True,
        include_side_reactions=True,
        include_catalyst_design=True,
        include_solvent_optimization=True,
        include_condition_optimization=True
    )
    
    # Perform analysis
    result = ultimate_system.analyze_reaction_ultimate(request)
    
    # Display optimization results
    if result.optimal_conditions:
        conditions = result.optimal_conditions.optimal_conditions
        print(f"\n🎯 OPTIMIZED CONDITIONS")
        print(f"Temperature: {conditions.temperature-273.15:.1f}°C")
        print(f"Pressure: {conditions.pressure:.1f} atm")
        print(f"Time: {conditions.time:.1f} hours")
        print(f"Catalyst loading: {conditions.catalyst_loading:.2%}")
    
    print("\n✅ Multi-objective optimization completed!")
    return result

def example_system_benchmark():
    """Example: Benchmark the ultimate system performance."""
    print("\n" + "="*80)
    print("🏃 SYSTEM BENCHMARK - ULTIMATE ANALYSIS")
    print("="*80)
    print("Objective: Test system performance on diverse reaction types")
    
    from ultimate_reaction_system import UltimateProfessionalReactionSystem, UltimateAnalysisRequest
    import time
    
    # Initialize system
    ultimate_system = UltimateProfessionalReactionSystem(dft_level='b3lyp/sto-3g')  # Faster basis
    
    # Test reactions
    test_reactions = [
        (['CCO', 'CC(=O)O'], ['CC(=O)OCC'], 'Esterification'),
        (['CC(=O)O', 'CCN'], ['CC(=O)NCC'], 'Amidation'),
        (['CCCl', 'O'], ['CCO'], 'SN2 Substitution'),
        (['c1ccccc1Br', 'c1ccccc1B(O)O'], ['c1ccc(-c2ccccc2)cc1'], 'Suzuki Coupling')
    ]
    
    benchmark_results = []
    
    for reactants, products, reaction_name in test_reactions:
        print(f"\nTesting: {reaction_name}")
        start_time = time.time()
        
        request = UltimateAnalysisRequest(
            reactant_smiles=reactants,
            target_products=products,
            include_ts_search=False,  # Skip for speed
            optimization_objectives=['yield', 'selectivity']
        )
        
        try:
            result = ultimate_system.analyze_reaction_ultimate(request)
            computation_time = time.time() - start_time
            
            benchmark_results.append({
                'reaction': reaction_name,
                'success': result.success_rate > 0.5,
                'confidence': result.confidence_score,
                'time': computation_time
            })
            
            print(f"   ✅ Success: {result.success_rate:.1%}, Time: {computation_time:.1f}s")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            benchmark_results.append({
                'reaction': reaction_name,
                'success': False,
                'confidence': 0.0,
                'time': time.time() - start_time
            })
    
    # Display benchmark summary
    print(f"\n📊 BENCHMARK SUMMARY")
    print("-" * 40)
    successful = sum(1 for r in benchmark_results if r['success'])
    total = len(benchmark_results)
    avg_time = sum(r['time'] for r in benchmark_results) / total
    avg_confidence = sum(r['confidence'] for r in benchmark_results if r['success']) / max(successful, 1)
    
    print(f"Success rate: {successful}/{total} ({successful/total:.1%})")
    print(f"Average time: {avg_time:.1f} seconds")
    print(f"Average confidence: {avg_confidence:.1%}")
    
    print("\n✅ System benchmark completed!")
    return benchmark_results

def run_all_ultimate_examples():
    """Run all ultimate system examples."""
    print("🚀 ULTIMATE PROFESSIONAL REACTION SYSTEM - COMPREHENSIVE EXAMPLES")
    print("=" * 80)
    
    examples = [
        ("Pharmaceutical Synthesis", example_pharmaceutical_synthesis),
        ("Green Chemistry Optimization", example_green_chemistry_optimization),
        ("Industrial Scale Optimization", example_industrial_scale_optimization),
        ("Catalyst Screening Study", example_catalyst_screening_study),
        ("Multi-Objective Optimization", example_multi_objective_optimization),
        ("System Benchmark", example_system_benchmark),
    ]
    
    results = {}
    
    for example_name, example_func in examples:
        print(f"\n{'='*20} {example_name} {'='*20}")
        try:
            result = example_func()
            results[example_name] = {'success': True, 'result': result}
        except Exception as e:
            print(f"❌ {example_name} failed: {e}")
            results[example_name] = {'success': False, 'error': str(e)}
    
    # Final summary
    print(f"\n{'='*80}")
    print("🏁 ULTIMATE EXAMPLES SUMMARY")
    print("=" * 80)
    
    for example_name, result_info in results.items():
        status = "✅ SUCCESS" if result_info['success'] else "❌ FAILED"
        print(f"{status} {example_name}")
    
    successful = sum(1 for r in results.values() if r['success'])
    total = len(results)
    print(f"\nOverall: {successful}/{total} examples completed successfully")
    
    if successful == total:
        print("\n🎉 ALL ULTIMATE EXAMPLES COMPLETED SUCCESSFULLY!")
        print("The Ultimate Professional Reaction System is ready for production use.")
    else:
        print("\n⚠️  Some examples had issues. Please review the output above.")

if __name__ == "__main__":
    run_all_ultimate_examples()
