"""
Ultimate Professional Reaction System
Master integration of all advanced features for professional computational chemistry.
This is the pinnacle system that combines all capabilities into a unified interface.
"""

import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass, asdict
import json
import time
from datetime import datetime

# Import all advanced systems
from advanced_reaction_system import AdvancedReactionSystem, ComprehensiveReactionAnalysis
from catalyst_design_system import IntelligentCatalystDesigner, CatalystScreeningResult
from solvent_effect_predictor import ComprehensiveSolventPredictor, SolventRecommendation
from reaction_condition_optimizer import ReactionConditionOptimizer, OptimizationResult, ReactionConditions, OptimizationObjective
from real_product_predictor import ReactionPrediction

@dataclass
class UltimateAnalysisRequest:
    """Complete analysis request with all parameters."""
    reactant_smiles: List[str]
    target_products: List[str] = None
    
    # Analysis scope
    include_product_prediction: bool = True
    include_pathway_analysis: bool = True
    include_side_reactions: bool = True
    include_catalyst_design: bool = True
    include_solvent_optimization: bool = True
    include_condition_optimization: bool = True
    include_ts_search: bool = False  # Expensive, off by default
    
    # Optimization objectives
    optimization_objectives: List[str] = None  # ['yield', 'selectivity', 'cost', 'safety']
    
    # Constraints
    cost_constraints: Dict[str, Any] = None
    safety_constraints: Dict[str, Any] = None
    environmental_constraints: Dict[str, Any] = None
    
    # Computational settings
    dft_level: str = 'b3lyp/6-31g*'
    max_computation_time: float = 3600.0  # seconds
    
    def __post_init__(self):
        if self.optimization_objectives is None:
            self.optimization_objectives = ['yield', 'selectivity']

@dataclass
class UltimateAnalysisResult:
    """Complete analysis result with all components."""
    request: UltimateAnalysisRequest
    
    # Core analysis results
    reaction_analysis: ComprehensiveReactionAnalysis = None
    catalyst_recommendations: CatalystScreeningResult = None
    solvent_recommendations: List[SolventRecommendation] = None
    optimal_conditions: OptimizationResult = None
    
    # Performance metrics
    computation_time: float = 0.0
    success_rate: float = 0.0
    confidence_score: float = 0.0
    
    # Executive summary
    executive_summary: Dict[str, Any] = None
    key_findings: List[str] = None
    actionable_recommendations: List[str] = None
    
    # Risk assessment
    risk_assessment: Dict[str, Any] = None
    
    # Economic analysis
    economic_analysis: Dict[str, Any] = None

class UltimateProfessionalReactionSystem:
    """
    The ultimate professional reaction analysis system.
    
    This is the master system that integrates all advanced capabilities:
    - Advanced Product Prediction with ML and templates
    - Multiple Pathway Explorer with network analysis
    - Side Reaction Predictor with selectivity optimization
    - Intelligent Catalyst Designer with performance prediction
    - Comprehensive Solvent Predictor with multi-objective optimization
    - Reaction Condition Optimizer with multi-algorithm support
    - Real Transition State Search with quantum chemistry
    
    Designed for professional chemists, process engineers, and researchers
    who need comprehensive, reliable, and actionable reaction analysis.
    """
    
    def __init__(self, dft_level: str = 'b3lyp/6-31g*'):
        """Initialize the ultimate reaction system."""
        print(f"🚀 INITIALIZING ULTIMATE PROFESSIONAL REACTION SYSTEM")
        print("=" * 70)
        
        # Parse DFT level
        if '/' in dft_level:
            self.xc, self.basis = dft_level.split('/')
        else:
            self.xc, self.basis = 'b3lyp', '6-31g*'
        
        print(f"DFT Level: {self.xc}/{self.basis}")
        
        # Initialize all subsystems
        print("Initializing subsystems...")
        self.reaction_system = AdvancedReactionSystem(self.xc, self.basis)
        self.catalyst_designer = IntelligentCatalystDesigner()
        self.solvent_predictor = ComprehensiveSolventPredictor()
        self.condition_optimizer = ReactionConditionOptimizer()
        
        # System capabilities
        self.capabilities = {
            'product_prediction': True,
            'pathway_analysis': True,
            'side_reaction_analysis': True,
            'catalyst_design': True,
            'solvent_optimization': True,
            'condition_optimization': True,
            'transition_state_search': True,
            'multi_objective_optimization': True,
            'risk_assessment': True,
            'economic_analysis': True
        }
        
        print("✅ All subsystems initialized successfully")
        print(f"Available capabilities: {len(self.capabilities)}")
        print("System ready for professional analysis")
    
    def analyze_reaction_ultimate(self, request: UltimateAnalysisRequest) -> UltimateAnalysisResult:
        """
        Perform the ultimate comprehensive reaction analysis.
        
        This is the master method that orchestrates all analysis components
        to provide the most comprehensive reaction analysis possible.
        """
        print(f"\n🎯 ULTIMATE PROFESSIONAL REACTION ANALYSIS")
        print("=" * 70)
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Reactants: {' + '.join(request.reactant_smiles)}")
        if request.target_products:
            print(f"Targets: {' + '.join(request.target_products)}")
        print(f"Analysis scope: {sum([request.include_product_prediction, request.include_pathway_analysis, request.include_side_reactions, request.include_catalyst_design, request.include_solvent_optimization, request.include_condition_optimization])} modules")
        
        start_time = time.time()
        result = UltimateAnalysisResult(request=request)
        
        try:
            # Phase 1: Core Reaction Analysis
            if request.include_product_prediction or request.include_pathway_analysis or request.include_side_reactions:
                print(f"\n📊 PHASE 1: Core Reaction Analysis")
                result.reaction_analysis = self._perform_core_analysis(request)
            
            # Phase 2: Catalyst Design
            if request.include_catalyst_design and result.reaction_analysis:
                print(f"\n🧪 PHASE 2: Intelligent Catalyst Design")
                result.catalyst_recommendations = self._perform_catalyst_design(request, result.reaction_analysis)
            
            # Phase 3: Solvent Optimization
            if request.include_solvent_optimization and result.reaction_analysis:
                print(f"\n🧪 PHASE 3: Comprehensive Solvent Optimization")
                result.solvent_recommendations = self._perform_solvent_optimization(request, result.reaction_analysis)
            
            # Phase 4: Condition Optimization
            if request.include_condition_optimization and result.reaction_analysis:
                print(f"\n🎯 PHASE 4: Multi-Objective Condition Optimization")
                result.optimal_conditions = self._perform_condition_optimization(request, result.reaction_analysis)
            
            # Phase 5: Integration and Analysis
            print(f"\n📈 PHASE 5: Integration and Executive Analysis")
            result = self._perform_integration_analysis(result)
            
            # Calculate performance metrics
            result.computation_time = time.time() - start_time
            result.success_rate = self._calculate_success_rate(result)
            result.confidence_score = self._calculate_confidence_score(result)
            
            # Display final results
            self._display_ultimate_results(result)
            
            return result
            
        except Exception as e:
            print(f"❌ Ultimate analysis failed: {e}")
            result.computation_time = time.time() - start_time
            result.success_rate = 0.0
            result.confidence_score = 0.0
            return result
    
    def _perform_core_analysis(self, request: UltimateAnalysisRequest) -> ComprehensiveReactionAnalysis:
        """Perform core reaction analysis."""
        # Prepare conditions
        reaction_conditions = {}
        if request.cost_constraints:
            reaction_conditions.update(request.cost_constraints)
        if request.safety_constraints:
            reaction_conditions.update(request.safety_constraints)
        
        # Perform comprehensive analysis
        analysis = self.reaction_system.analyze_reaction_comprehensive(
            reactant_smiles=request.reactant_smiles,
            target_products=request.target_products,
            reaction_conditions=reaction_conditions,
            include_ts_search=request.include_ts_search
        )
        
        return analysis
    
    def _perform_catalyst_design(self, request: UltimateAnalysisRequest, 
                               reaction_analysis: ComprehensiveReactionAnalysis) -> CatalystScreeningResult:
        """Perform intelligent catalyst design."""
        if not reaction_analysis.product_predictions:
            return None
        
        # Use the best reaction prediction for catalyst design
        best_reaction = reaction_analysis.product_predictions[0]
        
        # Convert dict to ReactionPrediction if needed
        if isinstance(best_reaction, dict):
            reaction_pred = ReactionPrediction(
                reaction_type=best_reaction.get('reaction_type', 'unknown'),
                description=best_reaction.get('description', ''),
                reactants=best_reaction.get('reactants', []),
                products=best_reaction.get('products', []),
                conditions=best_reaction.get('conditions', {}),
                confidence=best_reaction.get('confidence', 0.5),
                method=best_reaction.get('method', 'unknown')
            )
        else:
            reaction_pred = best_reaction
        
        # Set performance targets
        target_performance = {
            'activity': 0.8,
            'selectivity': 0.9
        }
        
        # Apply constraints
        constraints = {}
        if request.cost_constraints:
            constraints.update(request.cost_constraints)
        
        # Design catalyst
        catalyst_result = self.catalyst_designer.design_catalyst_for_reaction(
            reaction=reaction_pred,
            target_performance=target_performance,
            constraints=constraints
        )
        
        return catalyst_result
    
    def _perform_solvent_optimization(self, request: UltimateAnalysisRequest,
                                    reaction_analysis: ComprehensiveReactionAnalysis) -> List[SolventRecommendation]:
        """Perform comprehensive solvent optimization."""
        if not reaction_analysis.product_predictions:
            return []
        
        # Use the best reaction prediction
        best_reaction = reaction_analysis.product_predictions[0]
        
        # Convert dict to ReactionPrediction if needed
        if isinstance(best_reaction, dict):
            reaction_pred = ReactionPrediction(
                reaction_type=best_reaction.get('reaction_type', 'unknown'),
                description=best_reaction.get('description', ''),
                reactants=best_reaction.get('reactants', []),
                products=best_reaction.get('products', []),
                conditions=best_reaction.get('conditions', {}),
                confidence=best_reaction.get('confidence', 0.5),
                method=best_reaction.get('method', 'unknown')
            )
        else:
            reaction_pred = best_reaction
        
        # Set optimization objectives
        objectives = {}
        if 'yield' in request.optimization_objectives:
            objectives['rate'] = 1.0
        if 'selectivity' in request.optimization_objectives:
            objectives['selectivity'] = 1.0
        if 'cost' in request.optimization_objectives:
            objectives['cost'] = 0.5
        
        # Apply constraints
        constraints = {}
        if request.environmental_constraints:
            constraints.update(request.environmental_constraints)
        if request.safety_constraints:
            constraints.update(request.safety_constraints)
        
        # Optimize solvent
        solvent_recommendations = self.solvent_predictor.predict_optimal_solvent(
            reaction=reaction_pred,
            objectives=objectives,
            constraints=constraints
        )
        
        return solvent_recommendations
    
    def _perform_condition_optimization(self, request: UltimateAnalysisRequest,
                                      reaction_analysis: ComprehensiveReactionAnalysis) -> OptimizationResult:
        """Perform multi-objective condition optimization."""
        if not reaction_analysis.product_predictions:
            return None
        
        # Use the best reaction prediction
        best_reaction = reaction_analysis.product_predictions[0]
        
        # Convert dict to ReactionPrediction if needed
        if isinstance(best_reaction, dict):
            reaction_pred = ReactionPrediction(
                reaction_type=best_reaction.get('reaction_type', 'unknown'),
                description=best_reaction.get('description', ''),
                reactants=best_reaction.get('reactants', []),
                products=best_reaction.get('products', []),
                conditions=best_reaction.get('conditions', {}),
                confidence=best_reaction.get('confidence', 0.5),
                method=best_reaction.get('method', 'unknown')
            )
        else:
            reaction_pred = best_reaction
        
        # Create optimization objectives
        objectives = []
        for obj_name in request.optimization_objectives:
            if obj_name == 'yield':
                objectives.append(OptimizationObjective('yield', 0.9, weight=1.0))
            elif obj_name == 'selectivity':
                objectives.append(OptimizationObjective('selectivity', 0.9, weight=1.0))
            elif obj_name == 'cost':
                objectives.append(OptimizationObjective('cost', 1.0, weight=0.5, minimize=True))
            elif obj_name == 'safety':
                objectives.append(OptimizationObjective('safety', 0.8, weight=0.8))
        
        # Set initial conditions
        initial_conditions = ReactionConditions(
            temperature=298.15,
            pressure=1.0,
            time=2.0,
            catalyst_loading=0.05
        )
        
        # Optimize conditions
        optimization_result = self.condition_optimizer.optimize_reaction_conditions(
            reaction=reaction_pred,
            objectives=objectives,
            initial_conditions=initial_conditions,
            algorithm='evolutionary'
        )
        
        return optimization_result
    
    def _perform_integration_analysis(self, result: UltimateAnalysisResult) -> UltimateAnalysisResult:
        """Perform integration analysis and generate executive summary."""
        # Generate executive summary
        result.executive_summary = self._generate_executive_summary(result)
        
        # Extract key findings
        result.key_findings = self._extract_key_findings(result)
        
        # Generate actionable recommendations
        result.actionable_recommendations = self._generate_actionable_recommendations(result)
        
        # Perform risk assessment
        result.risk_assessment = self._perform_risk_assessment(result)
        
        # Perform economic analysis
        result.economic_analysis = self._perform_economic_analysis(result)
        
        return result
    
    def _generate_executive_summary(self, result: UltimateAnalysisResult) -> Dict[str, Any]:
        """Generate executive summary of the analysis."""
        summary = {
            'overall_feasibility': 'unknown',
            'recommended_approach': 'unknown',
            'key_challenges': [],
            'success_probability': 0.0,
            'estimated_development_time': 'unknown',
            'commercial_viability': 'unknown'
        }
        
        # Analyze overall feasibility
        if result.reaction_analysis and result.reaction_analysis.product_predictions:
            best_confidence = result.reaction_analysis.product_predictions[0].get('confidence', 0)
            if best_confidence > 0.8:
                summary['overall_feasibility'] = 'high'
                summary['success_probability'] = 0.8
            elif best_confidence > 0.6:
                summary['overall_feasibility'] = 'moderate'
                summary['success_probability'] = 0.6
            else:
                summary['overall_feasibility'] = 'challenging'
                summary['success_probability'] = 0.4
        
        # Determine recommended approach
        if result.catalyst_recommendations and result.catalyst_recommendations.top_catalysts:
            best_catalyst = result.catalyst_recommendations.top_catalysts[0]
            summary['recommended_approach'] = f"Catalytic approach using {best_catalyst.catalyst_name}"
        elif result.solvent_recommendations:
            best_solvent = result.solvent_recommendations[0]
            summary['recommended_approach'] = f"Optimized conditions in {best_solvent.solvent_name}"
        else:
            summary['recommended_approach'] = "Direct thermal approach"
        
        # Identify key challenges
        if result.reaction_analysis and result.reaction_analysis.side_reaction_analysis:
            if result.reaction_analysis.side_reaction_analysis.overall_selectivity_estimate < 0.7:
                summary['key_challenges'].append("Selectivity optimization required")
        
        if result.optimal_conditions:
            if result.optimal_conditions.optimal_conditions.temperature > 400:
                summary['key_challenges'].append("High temperature operation")
            if result.optimal_conditions.optimal_conditions.pressure > 5:
                summary['key_challenges'].append("High pressure requirements")
        
        return summary
    
    def _extract_key_findings(self, result: UltimateAnalysisResult) -> List[str]:
        """Extract key findings from the analysis."""
        findings = []
        
        # Product prediction findings
        if result.reaction_analysis and result.reaction_analysis.product_predictions:
            n_products = len(result.reaction_analysis.product_predictions)
            findings.append(f"{n_products} viable reaction pathway(s) identified")
            
            best_confidence = result.reaction_analysis.product_predictions[0].get('confidence', 0)
            findings.append(f"Highest confidence prediction: {best_confidence:.1%}")
        
        # Catalyst findings
        if result.catalyst_recommendations and result.catalyst_recommendations.top_catalysts:
            best_catalyst = result.catalyst_recommendations.top_catalysts[0]
            findings.append(f"Optimal catalyst: {best_catalyst.catalyst_name} (score: {best_catalyst.confidence:.2f})")
        
        # Solvent findings
        if result.solvent_recommendations:
            best_solvent = result.solvent_recommendations[0]
            findings.append(f"Optimal solvent: {best_solvent.solvent_name} (score: {best_solvent.suitability_score:.2f})")
        
        # Condition findings
        if result.optimal_conditions:
            conditions = result.optimal_conditions.optimal_conditions
            findings.append(f"Optimal temperature: {conditions.temperature-273.15:.0f}°C")
            findings.append(f"Optimal reaction time: {conditions.time:.1f} hours")
        
        return findings
    
    def _generate_actionable_recommendations(self, result: UltimateAnalysisResult) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        # High-level strategy
        if result.executive_summary:
            feasibility = result.executive_summary.get('overall_feasibility', 'unknown')
            if feasibility == 'high':
                recommendations.append("Proceed with experimental validation - high success probability")
            elif feasibility == 'moderate':
                recommendations.append("Conduct small-scale optimization studies before scale-up")
            else:
                recommendations.append("Consider alternative synthetic routes or conditions")
        
        # Specific technical recommendations
        if result.catalyst_recommendations:
            recommendations.append("Implement recommended catalyst system with proper handling procedures")
        
        if result.solvent_recommendations:
            recommendations.append("Use optimized solvent system for improved performance")
        
        if result.optimal_conditions:
            recommendations.append("Apply optimized reaction conditions with careful monitoring")
        
        # Risk mitigation
        if result.risk_assessment:
            high_risks = [k for k, v in result.risk_assessment.items() if isinstance(v, dict) and v.get('level') == 'high']
            if high_risks:
                recommendations.append(f"Address high-risk factors: {', '.join(high_risks)}")
        
        return recommendations
    
    def _perform_risk_assessment(self, result: UltimateAnalysisResult) -> Dict[str, Any]:
        """Perform comprehensive risk assessment."""
        risks = {
            'technical_risk': {'level': 'low', 'factors': []},
            'safety_risk': {'level': 'low', 'factors': []},
            'economic_risk': {'level': 'low', 'factors': []},
            'environmental_risk': {'level': 'low', 'factors': []}
        }
        
        # Technical risk assessment
        if result.reaction_analysis:
            if not result.reaction_analysis.product_predictions:
                risks['technical_risk']['level'] = 'high'
                risks['technical_risk']['factors'].append('No viable pathways identified')
            elif result.reaction_analysis.product_predictions[0].get('confidence', 0) < 0.5:
                risks['technical_risk']['level'] = 'medium'
                risks['technical_risk']['factors'].append('Low prediction confidence')
        
        # Safety risk assessment
        if result.optimal_conditions:
            conditions = result.optimal_conditions.optimal_conditions
            if conditions.temperature > 450:
                risks['safety_risk']['level'] = 'high'
                risks['safety_risk']['factors'].append('Very high temperature operation')
            if conditions.pressure > 10:
                risks['safety_risk']['level'] = 'high'
                risks['safety_risk']['factors'].append('High pressure operation')
        
        return risks
    
    def _perform_economic_analysis(self, result: UltimateAnalysisResult) -> Dict[str, Any]:
        """Perform economic analysis."""
        analysis = {
            'cost_estimate': 'moderate',
            'cost_drivers': [],
            'economic_viability': 'viable',
            'payback_estimate': 'unknown'
        }
        
        # Analyze cost drivers
        if result.catalyst_recommendations and result.catalyst_recommendations.top_catalysts:
            best_catalyst = result.catalyst_recommendations.top_catalysts[0]
            if best_catalyst.cost_estimate in ['high', 'very_high']:
                analysis['cost_drivers'].append('Expensive catalyst')
                analysis['cost_estimate'] = 'high'
        
        if result.optimal_conditions:
            conditions = result.optimal_conditions.optimal_conditions
            if conditions.temperature > 400 or conditions.pressure > 5:
                analysis['cost_drivers'].append('High energy requirements')
        
        return analysis
    
    def _calculate_success_rate(self, result: UltimateAnalysisResult) -> float:
        """Calculate overall success rate of the analysis."""
        success_factors = []
        
        if result.reaction_analysis:
            success_factors.append(1.0)
        else:
            success_factors.append(0.0)
        
        if result.catalyst_recommendations:
            success_factors.append(1.0)
        else:
            success_factors.append(0.5)  # Partial credit
        
        if result.solvent_recommendations:
            success_factors.append(1.0)
        else:
            success_factors.append(0.5)
        
        if result.optimal_conditions:
            success_factors.append(1.0)
        else:
            success_factors.append(0.5)
        
        return np.mean(success_factors)
    
    def _calculate_confidence_score(self, result: UltimateAnalysisResult) -> float:
        """Calculate overall confidence score."""
        if not result.reaction_analysis or not result.reaction_analysis.product_predictions:
            return 0.0
        
        # Base confidence from reaction prediction
        base_confidence = result.reaction_analysis.product_predictions[0].get('confidence', 0)
        
        # Boost confidence if multiple systems agree
        confidence_boost = 0.0
        if result.catalyst_recommendations:
            confidence_boost += 0.1
        if result.solvent_recommendations:
            confidence_boost += 0.1
        if result.optimal_conditions:
            confidence_boost += 0.1
        
        return min(base_confidence + confidence_boost, 1.0)
    
    def _display_ultimate_results(self, result: UltimateAnalysisResult):
        """Display ultimate analysis results."""
        print(f"\n🏆 ULTIMATE ANALYSIS RESULTS")
        print("=" * 70)
        
        # Performance metrics
        print(f"Computation time: {result.computation_time:.1f} seconds")
        print(f"Success rate: {result.success_rate:.1%}")
        print(f"Confidence score: {result.confidence_score:.1%}")
        
        # Executive summary
        if result.executive_summary:
            print(f"\n📋 EXECUTIVE SUMMARY")
            print(f"Overall feasibility: {result.executive_summary['overall_feasibility'].upper()}")
            print(f"Recommended approach: {result.executive_summary['recommended_approach']}")
            print(f"Success probability: {result.executive_summary['success_probability']:.1%}")
        
        # Key findings
        if result.key_findings:
            print(f"\n🔍 KEY FINDINGS")
            for i, finding in enumerate(result.key_findings, 1):
                print(f"{i}. {finding}")
        
        # Actionable recommendations
        if result.actionable_recommendations:
            print(f"\n💡 ACTIONABLE RECOMMENDATIONS")
            for i, rec in enumerate(result.actionable_recommendations, 1):
                print(f"{i}. {rec}")
        
        print("=" * 70)
        print("🎉 ULTIMATE ANALYSIS COMPLETE")
    
    def export_ultimate_results(self, result: UltimateAnalysisResult, filename: str):
        """Export ultimate analysis results."""
        # Convert to serializable format
        export_data = asdict(result)
        
        # Handle non-serializable objects
        def clean_for_json(obj):
            if isinstance(obj, dict):
                return {k: clean_for_json(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [clean_for_json(item) for item in obj]
            elif hasattr(obj, '__dict__'):
                return clean_for_json(obj.__dict__)
            else:
                return str(obj) if not isinstance(obj, (int, float, str, bool, type(None))) else obj
        
        cleaned_data = clean_for_json(export_data)
        
        with open(filename, 'w') as f:
            json.dump(cleaned_data, f, indent=2, default=str)
        
        print(f"✅ Ultimate analysis results exported to {filename}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        return {
            'system_name': 'Ultimate Professional Reaction System',
            'version': '1.0.0',
            'dft_level': f"{self.xc}/{self.basis}",
            'capabilities': self.capabilities,
            'subsystems': {
                'reaction_system': 'AdvancedReactionSystem',
                'catalyst_designer': 'IntelligentCatalystDesigner',
                'solvent_predictor': 'ComprehensiveSolventPredictor',
                'condition_optimizer': 'ReactionConditionOptimizer'
            },
            'status': 'ready',
            'last_updated': datetime.now().isoformat()
        }
