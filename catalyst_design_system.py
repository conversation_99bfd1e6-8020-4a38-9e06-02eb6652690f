"""
Intelligent Catalyst Design System
Professional system for catalyst design, screening, and optimization.
Uses electronic structure analysis, descriptor-based design, and machine learning.
"""

import numpy as np
from rdkit import Chem
from rdkit.Chem import AllChem, Descriptors, rdMolDescriptors
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass, asdict
from collections import defaultdict
import itertools
import json
from real_product_predictor import AdvancedProductPredictor, ReactionPrediction

@dataclass
class CatalystDescriptors:
    """Electronic and steric descriptors for catalyst characterization."""
    # Electronic descriptors
    homo_energy: float = 0.0
    lumo_energy: float = 0.0
    electronegativity: float = 0.0
    hardness: float = 0.0
    electrophilicity: float = 0.0
    
    # Steric descriptors
    buried_volume: float = 0.0
    cone_angle: float = 0.0
    steric_hindrance_index: float = 0.0
    
    # Binding descriptors
    binding_affinity: float = 0.0
    activation_strength: float = 0.0
    
    # Selectivity descriptors
    enantioselectivity_potential: float = 0.0
    regioselectivity_potential: float = 0.0

@dataclass
class CatalystRecommendation:
    """Catalyst recommendation with detailed analysis."""
    catalyst_smiles: str
    catalyst_name: str
    catalyst_type: str
    descriptors: CatalystDescriptors
    predicted_performance: Dict[str, float]
    confidence: float
    rationale: List[str]
    experimental_precedent: List[str]
    cost_estimate: str
    availability: str

@dataclass
class CatalystScreeningResult:
    """Results from catalyst screening."""
    reaction: ReactionPrediction
    top_catalysts: List[CatalystRecommendation]
    screening_summary: Dict[str, any]
    optimization_suggestions: List[str]

class IntelligentCatalystDesigner:
    """
    Professional catalyst design and screening system.
    
    Features:
    - Electronic structure-based catalyst design
    - Descriptor-based catalyst screening
    - Machine learning catalyst performance prediction
    - Mechanistic analysis of catalyst effects
    - Cost and availability assessment
    """
    
    def __init__(self):
        """Initialize the catalyst design system."""
        self.catalyst_database = self._load_catalyst_database()
        self.reaction_catalyst_map = self._load_reaction_catalyst_mapping()
        self.descriptor_models = self._initialize_descriptor_models()
        self.performance_models = self._initialize_performance_models()
        
    def _load_catalyst_database(self) -> Dict[str, Dict]:
        """Load comprehensive catalyst database."""
        return {
            # Transition metal catalysts
            'palladium_catalysts': {
                'Pd(PPh3)4': {
                    'smiles': '[Pd]',  # Simplified representation
                    'type': 'cross_coupling',
                    'reactions': ['suzuki', 'heck', 'sonogashira'],
                    'electronic_properties': {'d_electrons': 10, 'oxidation_state': 0},
                    'ligands': ['PPh3'],
                    'cost': 'high',
                    'availability': 'commercial'
                },
                'Pd(OAc)2': {
                    'smiles': '[Pd+2]',
                    'type': 'oxidative_coupling',
                    'reactions': ['c_h_activation', 'oxidative_coupling'],
                    'electronic_properties': {'d_electrons': 8, 'oxidation_state': 2},
                    'ligands': ['acetate'],
                    'cost': 'moderate',
                    'availability': 'commercial'
                }
            },
            
            'ruthenium_catalysts': {
                'Grubbs_G2': {
                    'smiles': '[Ru]',  # Simplified
                    'type': 'metathesis',
                    'reactions': ['olefin_metathesis', 'ring_closing_metathesis'],
                    'electronic_properties': {'d_electrons': 6, 'oxidation_state': 2},
                    'ligands': ['NHC', 'PCy3'],
                    'cost': 'high',
                    'availability': 'commercial'
                }
            },
            
            'rhodium_catalysts': {
                'Rh(PPh3)3Cl': {
                    'smiles': '[Rh]',
                    'type': 'hydrogenation',
                    'reactions': ['hydrogenation', 'hydroformylation'],
                    'electronic_properties': {'d_electrons': 8, 'oxidation_state': 1},
                    'ligands': ['PPh3', 'Cl'],
                    'cost': 'very_high',
                    'availability': 'commercial'
                }
            },
            
            # Organocatalysts
            'organocatalysts': {
                'proline': {
                    'smiles': 'N1CCCC1C(=O)O',
                    'type': 'asymmetric_organocatalyst',
                    'reactions': ['aldol', 'mannich', 'michael_addition'],
                    'electronic_properties': {'nucleophilicity': 'moderate', 'basicity': 'weak'},
                    'cost': 'low',
                    'availability': 'commercial'
                },
                'DMAP': {
                    'smiles': 'CN(C)c1ccncc1',
                    'type': 'nucleophilic_catalyst',
                    'reactions': ['acylation', 'esterification'],
                    'electronic_properties': {'nucleophilicity': 'high', 'basicity': 'strong'},
                    'cost': 'low',
                    'availability': 'commercial'
                }
            },
            
            # Acid catalysts
            'acid_catalysts': {
                'TfOH': {
                    'smiles': 'OS(=O)(=O)C(F)(F)F',
                    'type': 'strong_acid',
                    'reactions': ['friedel_crafts', 'esterification', 'dehydration'],
                    'electronic_properties': {'acidity': 'very_strong', 'nucleophilicity': 'very_weak'},
                    'cost': 'moderate',
                    'availability': 'commercial'
                },
                'p_TsOH': {
                    'smiles': 'Cc1ccc(S(=O)(=O)O)cc1',
                    'type': 'moderate_acid',
                    'reactions': ['esterification', 'protection_deprotection'],
                    'electronic_properties': {'acidity': 'moderate', 'nucleophilicity': 'weak'},
                    'cost': 'low',
                    'availability': 'commercial'
                }
            },
            
            # Base catalysts
            'base_catalysts': {
                'DBU': {
                    'smiles': 'C1CCN2CCCN=C2C1',
                    'type': 'strong_base',
                    'reactions': ['elimination', 'isomerization', 'michael_addition'],
                    'electronic_properties': {'basicity': 'very_strong', 'nucleophilicity': 'moderate'},
                    'cost': 'moderate',
                    'availability': 'commercial'
                },
                'Et3N': {
                    'smiles': 'CCN(CC)CC',
                    'type': 'moderate_base',
                    'reactions': ['acylation', 'alkylation'],
                    'electronic_properties': {'basicity': 'moderate', 'nucleophilicity': 'weak'},
                    'cost': 'low',
                    'availability': 'commercial'
                }
            }
        }
    
    def _load_reaction_catalyst_mapping(self) -> Dict[str, List[str]]:
        """Load mapping of reaction types to suitable catalyst types."""
        return {
            'esterification': ['acid_catalysts', 'organocatalysts'],
            'amidation': ['organocatalysts', 'base_catalysts'],
            'suzuki_coupling': ['palladium_catalysts'],
            'heck_reaction': ['palladium_catalysts'],
            'olefin_metathesis': ['ruthenium_catalysts'],
            'hydrogenation': ['rhodium_catalysts', 'palladium_catalysts'],
            'aldol_condensation': ['organocatalysts', 'base_catalysts'],
            'michael_addition': ['organocatalysts', 'base_catalysts'],
            'friedel_crafts': ['acid_catalysts'],
            'sn2_substitution': ['base_catalysts'],
            'elimination': ['base_catalysts', 'acid_catalysts'],
            'cycloaddition': ['acid_catalysts', 'organocatalysts'],
            'oxidation': ['transition_metal_catalysts'],
            'reduction': ['rhodium_catalysts', 'palladium_catalysts']
        }
    
    def _initialize_descriptor_models(self) -> Dict:
        """Initialize models for calculating catalyst descriptors."""
        return {
            'electronic_model': self._create_electronic_descriptor_model(),
            'steric_model': self._create_steric_descriptor_model(),
            'binding_model': self._create_binding_descriptor_model()
        }
    
    def _create_electronic_descriptor_model(self):
        """Create model for electronic descriptor calculation."""
        class ElectronicDescriptorModel:
            def calculate_descriptors(self, mol: Chem.Mol) -> Dict[str, float]:
                """Calculate electronic descriptors for a molecule."""
                if not mol:
                    return {}
                
                # Simplified electronic descriptors
                descriptors = {}
                
                # Basic molecular properties
                descriptors['molecular_weight'] = Descriptors.MolWt(mol)
                descriptors['logp'] = Descriptors.MolLogP(mol)
                descriptors['tpsa'] = Descriptors.TPSA(mol)
                
                # Electronic properties (simplified estimates)
                num_aromatic_atoms = sum(1 for atom in mol.GetAtoms() if atom.GetIsAromatic())
                num_heteroatoms = sum(1 for atom in mol.GetAtoms() if atom.GetAtomicNum() not in [1, 6])
                
                descriptors['aromaticity'] = num_aromatic_atoms / mol.GetNumAtoms() if mol.GetNumAtoms() > 0 else 0
                descriptors['heteroatom_ratio'] = num_heteroatoms / mol.GetNumAtoms() if mol.GetNumAtoms() > 0 else 0
                
                # Estimate HOMO/LUMO (very simplified)
                descriptors['homo_estimate'] = -5.0 - descriptors['aromaticity'] * 2.0
                descriptors['lumo_estimate'] = -2.0 + descriptors['heteroatom_ratio'] * 3.0
                
                return descriptors
        
        return ElectronicDescriptorModel()
    
    def _create_steric_descriptor_model(self):
        """Create model for steric descriptor calculation."""
        class StericDescriptorModel:
            def calculate_descriptors(self, mol: Chem.Mol) -> Dict[str, float]:
                """Calculate steric descriptors for a molecule."""
                if not mol:
                    return {}
                
                descriptors = {}
                
                # Basic steric properties
                descriptors['num_rotatable_bonds'] = Descriptors.NumRotatableBonds(mol)
                descriptors['num_rings'] = Descriptors.RingCount(mol)
                descriptors['molecular_volume'] = self._estimate_molecular_volume(mol)
                
                # Steric hindrance estimates
                descriptors['branching_index'] = self._calculate_branching_index(mol)
                descriptors['steric_bulk'] = descriptors['molecular_volume'] / mol.GetNumAtoms() if mol.GetNumAtoms() > 0 else 0
                
                return descriptors
            
            def _estimate_molecular_volume(self, mol: Chem.Mol) -> float:
                """Estimate molecular volume using simple additive method."""
                # Simplified volume estimation
                atom_volumes = {'C': 20.0, 'N': 15.0, 'O': 14.0, 'S': 25.0, 'P': 30.0, 'H': 5.0}
                total_volume = 0.0
                
                for atom in mol.GetAtoms():
                    symbol = atom.GetSymbol()
                    total_volume += atom_volumes.get(symbol, 20.0)
                
                return total_volume
            
            def _calculate_branching_index(self, mol: Chem.Mol) -> float:
                """Calculate branching index of the molecule."""
                if mol.GetNumAtoms() == 0:
                    return 0.0
                
                total_degree = sum(atom.GetDegree() for atom in mol.GetAtoms())
                return total_degree / mol.GetNumAtoms()
        
        return StericDescriptorModel()
    
    def _create_binding_descriptor_model(self):
        """Create model for binding descriptor calculation."""
        class BindingDescriptorModel:
            def calculate_descriptors(self, mol: Chem.Mol, reaction_type: str) -> Dict[str, float]:
                """Calculate binding-related descriptors."""
                if not mol:
                    return {}
                
                descriptors = {}
                
                # Simplified binding affinity estimates based on functional groups
                binding_groups = {
                    'phosphine': 0.8,
                    'amine': 0.6,
                    'carbonyl': 0.7,
                    'aromatic': 0.5,
                    'halide': 0.4
                }
                
                # Identify functional groups (simplified)
                smiles = Chem.MolToSmiles(mol)
                binding_score = 0.0
                
                if 'P' in smiles:
                    binding_score += binding_groups['phosphine']
                if 'N' in smiles and 'C=O' not in smiles:
                    binding_score += binding_groups['amine']
                if 'C=O' in smiles:
                    binding_score += binding_groups['carbonyl']
                if any(atom.GetIsAromatic() for atom in mol.GetAtoms()):
                    binding_score += binding_groups['aromatic']
                
                descriptors['binding_affinity_estimate'] = min(binding_score, 1.0)
                descriptors['coordination_sites'] = self._count_coordination_sites(mol)
                
                return descriptors
            
            def _count_coordination_sites(self, mol: Chem.Mol) -> int:
                """Count potential coordination sites."""
                coordination_atoms = ['N', 'O', 'S', 'P']
                count = 0
                
                for atom in mol.GetAtoms():
                    if atom.GetSymbol() in coordination_atoms:
                        # Check if atom has lone pairs (simplified)
                        if atom.GetTotalNumHs() < atom.GetTotalValence():
                            count += 1
                
                return count
        
        return BindingDescriptorModel()
    
    def _initialize_performance_models(self) -> Dict:
        """Initialize models for predicting catalyst performance."""
        return {
            'activity_model': self._create_activity_model(),
            'selectivity_model': self._create_selectivity_model(),
            'stability_model': self._create_stability_model()
        }
    
    def _create_activity_model(self):
        """Create model for predicting catalyst activity."""
        class ActivityModel:
            def predict_activity(self, descriptors: Dict[str, float], reaction_type: str) -> float:
                """Predict catalyst activity based on descriptors."""
                # Simplified activity prediction
                base_activity = 0.5
                
                # Electronic effects
                if 'homo_estimate' in descriptors:
                    homo = descriptors['homo_estimate']
                    if -6.0 < homo < -4.0:  # Optimal HOMO range
                        base_activity += 0.2
                
                # Steric effects
                if 'steric_bulk' in descriptors:
                    bulk = descriptors['steric_bulk']
                    if reaction_type in ['hydrogenation', 'oxidation']:
                        # Less steric hindrance better for these reactions
                        base_activity += max(0, 0.3 - bulk * 0.1)
                    else:
                        # Some steric bulk can be beneficial for selectivity
                        base_activity += min(0.2, bulk * 0.05)
                
                # Binding effects
                if 'binding_affinity_estimate' in descriptors:
                    binding = descriptors['binding_affinity_estimate']
                    base_activity += binding * 0.3
                
                return min(base_activity, 1.0)
        
        return ActivityModel()
    
    def _create_selectivity_model(self):
        """Create model for predicting catalyst selectivity."""
        class SelectivityModel:
            def predict_selectivity(self, descriptors: Dict[str, float], reaction_type: str) -> Dict[str, float]:
                """Predict different types of selectivity."""
                selectivity = {
                    'chemoselectivity': 0.7,
                    'regioselectivity': 0.6,
                    'stereoselectivity': 0.5
                }
                
                # Steric effects on selectivity
                if 'steric_bulk' in descriptors:
                    bulk = descriptors['steric_bulk']
                    selectivity['stereoselectivity'] += min(0.3, bulk * 0.1)
                    selectivity['regioselectivity'] += min(0.2, bulk * 0.08)
                
                # Electronic effects
                if 'heteroatom_ratio' in descriptors:
                    hetero_ratio = descriptors['heteroatom_ratio']
                    selectivity['chemoselectivity'] += min(0.2, hetero_ratio * 0.5)
                
                return {k: min(v, 1.0) for k, v in selectivity.items()}
        
        return SelectivityModel()
    
    def _create_stability_model(self):
        """Create model for predicting catalyst stability."""
        class StabilityModel:
            def predict_stability(self, descriptors: Dict[str, float], conditions: Dict[str, str]) -> float:
                """Predict catalyst stability under given conditions."""
                base_stability = 0.7
                
                # Temperature effects
                temp = conditions.get('temperature', 'moderate')
                if temp == 'high':
                    base_stability -= 0.2
                elif temp == 'low':
                    base_stability += 0.1
                
                # Solvent effects
                solvent = conditions.get('solvent', 'organic')
                if solvent in ['water', 'polar_protic']:
                    base_stability -= 0.15
                
                # Molecular stability factors
                if 'num_rings' in descriptors:
                    rings = descriptors['num_rings']
                    base_stability += min(0.2, rings * 0.05)  # Rings add stability
                
                return min(base_stability, 1.0)
        
        return StabilityModel()

    def design_catalyst_for_reaction(self, reaction: ReactionPrediction,
                                   target_performance: Dict[str, float] = None,
                                   constraints: Dict[str, any] = None) -> CatalystScreeningResult:
        """
        Design optimal catalyst for a specific reaction.

        Args:
            reaction: The reaction to catalyze
            target_performance: Desired performance metrics
            constraints: Design constraints (cost, availability, etc.)

        Returns:
            Catalyst screening results with recommendations
        """
        print(f"\n🧪 INTELLIGENT CATALYST DESIGN")
        print("=" * 50)
        print(f"Reaction: {reaction.reaction_type}")
        print(f"Description: {reaction.description}")

        if target_performance:
            print(f"Target performance: {target_performance}")
        if constraints:
            print(f"Constraints: {constraints}")

        # Step 1: Identify suitable catalyst types
        suitable_catalyst_types = self._identify_suitable_catalyst_types(reaction)
        print(f"\nSuitable catalyst types: {suitable_catalyst_types}")

        # Step 2: Screen catalysts from database
        candidate_catalysts = self._screen_catalyst_database(
            reaction, suitable_catalyst_types, constraints
        )

        # Step 3: Calculate descriptors for each catalyst
        catalyst_recommendations = []

        for catalyst_data in candidate_catalysts:
            recommendation = self._evaluate_catalyst(
                catalyst_data, reaction, target_performance
            )
            if recommendation:
                catalyst_recommendations.append(recommendation)

        # Step 4: Rank catalysts
        ranked_catalysts = self._rank_catalysts(
            catalyst_recommendations, target_performance
        )

        # Step 5: Generate optimization suggestions
        optimization_suggestions = self._generate_optimization_suggestions(
            ranked_catalysts, reaction
        )

        # Create screening result
        screening_result = CatalystScreeningResult(
            reaction=reaction,
            top_catalysts=ranked_catalysts[:10],  # Top 10
            screening_summary=self._generate_screening_summary(
                ranked_catalysts, suitable_catalyst_types
            ),
            optimization_suggestions=optimization_suggestions
        )

        # Display results
        self._display_catalyst_recommendations(screening_result)

        return screening_result

    def _identify_suitable_catalyst_types(self, reaction: ReactionPrediction) -> List[str]:
        """Identify catalyst types suitable for the reaction."""
        reaction_type = reaction.reaction_type.lower()

        # Direct mapping
        if reaction_type in self.reaction_catalyst_map:
            return self.reaction_catalyst_map[reaction_type]

        # Pattern matching for similar reactions
        suitable_types = []

        if 'coupling' in reaction_type or 'cross' in reaction_type:
            suitable_types.extend(['palladium_catalysts'])

        if 'hydrogenation' in reaction_type or 'reduction' in reaction_type:
            suitable_types.extend(['rhodium_catalysts', 'palladium_catalysts'])

        if 'oxidation' in reaction_type:
            suitable_types.extend(['ruthenium_catalysts', 'palladium_catalysts'])

        if 'acid' in reaction.description.lower() or 'esterification' in reaction_type:
            suitable_types.extend(['acid_catalysts'])

        if 'base' in reaction.description.lower() or 'elimination' in reaction_type:
            suitable_types.extend(['base_catalysts'])

        if 'asymmetric' in reaction.description.lower():
            suitable_types.extend(['organocatalysts'])

        # Default fallback
        if not suitable_types:
            suitable_types = ['organocatalysts', 'acid_catalysts', 'base_catalysts']

        return list(set(suitable_types))

    def _screen_catalyst_database(self, reaction: ReactionPrediction,
                                suitable_types: List[str],
                                constraints: Dict[str, any] = None) -> List[Dict]:
        """Screen catalyst database for suitable candidates."""
        candidates = []

        for catalyst_type in suitable_types:
            if catalyst_type in self.catalyst_database:
                type_catalysts = self.catalyst_database[catalyst_type]

                for catalyst_name, catalyst_data in type_catalysts.items():
                    # Apply constraints
                    if constraints:
                        if not self._meets_constraints(catalyst_data, constraints):
                            continue

                    # Check if catalyst is suitable for this reaction
                    if self._is_catalyst_suitable(catalyst_data, reaction):
                        catalyst_info = catalyst_data.copy()
                        catalyst_info['name'] = catalyst_name
                        catalyst_info['type'] = catalyst_type
                        candidates.append(catalyst_info)

        return candidates

    def _meets_constraints(self, catalyst_data: Dict, constraints: Dict[str, any]) -> bool:
        """Check if catalyst meets design constraints."""
        if 'max_cost' in constraints:
            cost_levels = {'low': 1, 'moderate': 2, 'high': 3, 'very_high': 4}
            catalyst_cost = cost_levels.get(catalyst_data.get('cost', 'moderate'), 2)
            max_cost = cost_levels.get(constraints['max_cost'], 4)

            if catalyst_cost > max_cost:
                return False

        if 'availability' in constraints:
            required_availability = constraints['availability']
            catalyst_availability = catalyst_data.get('availability', 'unknown')

            if required_availability == 'commercial' and catalyst_availability != 'commercial':
                return False

        if 'exclude_metals' in constraints:
            excluded_metals = constraints['exclude_metals']
            catalyst_smiles = catalyst_data.get('smiles', '')

            for metal in excluded_metals:
                if metal in catalyst_smiles:
                    return False

        return True

    def _is_catalyst_suitable(self, catalyst_data: Dict, reaction: ReactionPrediction) -> bool:
        """Check if catalyst is suitable for the specific reaction."""
        # Check if reaction type is in catalyst's reaction list
        catalyst_reactions = catalyst_data.get('reactions', [])
        reaction_type = reaction.reaction_type.lower()

        # Direct match
        if reaction_type in catalyst_reactions:
            return True

        # Pattern matching
        for cat_reaction in catalyst_reactions:
            if cat_reaction in reaction_type or reaction_type in cat_reaction:
                return True

        # Check reaction conditions compatibility
        reaction_conditions = reaction.conditions

        # Temperature compatibility
        if 'temperature' in reaction_conditions:
            temp = reaction_conditions['temperature']
            if temp == 'high' and catalyst_data.get('thermal_stability', 'moderate') == 'low':
                return False

        return True

    def _evaluate_catalyst(self, catalyst_data: Dict, reaction: ReactionPrediction,
                         target_performance: Dict[str, float] = None) -> Optional[CatalystRecommendation]:
        """Evaluate a catalyst and create recommendation."""
        try:
            # Create molecule from SMILES
            mol = Chem.MolFromSmiles(catalyst_data['smiles'])
            if not mol:
                return None

            # Calculate descriptors
            electronic_desc = self.descriptor_models['electronic_model'].calculate_descriptors(mol)
            steric_desc = self.descriptor_models['steric_model'].calculate_descriptors(mol)
            binding_desc = self.descriptor_models['binding_model'].calculate_descriptors(
                mol, reaction.reaction_type
            )

            # Combine descriptors
            all_descriptors = {**electronic_desc, **steric_desc, **binding_desc}

            # Create CatalystDescriptors object
            descriptors = CatalystDescriptors(
                homo_energy=all_descriptors.get('homo_estimate', 0.0),
                lumo_energy=all_descriptors.get('lumo_estimate', 0.0),
                buried_volume=all_descriptors.get('molecular_volume', 0.0),
                steric_hindrance_index=all_descriptors.get('steric_bulk', 0.0),
                binding_affinity=all_descriptors.get('binding_affinity_estimate', 0.0)
            )

            # Predict performance
            activity = self.performance_models['activity_model'].predict_activity(
                all_descriptors, reaction.reaction_type
            )
            selectivity = self.performance_models['selectivity_model'].predict_selectivity(
                all_descriptors, reaction.reaction_type
            )
            stability = self.performance_models['stability_model'].predict_stability(
                all_descriptors, reaction.conditions
            )

            predicted_performance = {
                'activity': activity,
                'stability': stability,
                **selectivity
            }

            # Calculate overall confidence
            confidence = self._calculate_catalyst_confidence(
                predicted_performance, catalyst_data, reaction
            )

            # Generate rationale
            rationale = self._generate_catalyst_rationale(
                catalyst_data, predicted_performance, reaction
            )

            # Create recommendation
            recommendation = CatalystRecommendation(
                catalyst_smiles=catalyst_data['smiles'],
                catalyst_name=catalyst_data['name'],
                catalyst_type=catalyst_data['type'],
                descriptors=descriptors,
                predicted_performance=predicted_performance,
                confidence=confidence,
                rationale=rationale,
                experimental_precedent=catalyst_data.get('reactions', []),
                cost_estimate=catalyst_data.get('cost', 'unknown'),
                availability=catalyst_data.get('availability', 'unknown')
            )

            return recommendation

        except Exception as e:
            print(f"   Warning: Could not evaluate catalyst {catalyst_data.get('name', 'unknown')}: {e}")
            return None

    def _calculate_catalyst_confidence(self, performance: Dict[str, float],
                                     catalyst_data: Dict, reaction: ReactionPrediction) -> float:
        """Calculate confidence in catalyst recommendation."""
        base_confidence = 0.5

        # Performance-based confidence
        avg_performance = np.mean(list(performance.values()))
        base_confidence += avg_performance * 0.3

        # Literature precedent
        if reaction.reaction_type in catalyst_data.get('reactions', []):
            base_confidence += 0.2

        # Commercial availability
        if catalyst_data.get('availability') == 'commercial':
            base_confidence += 0.1

        return min(base_confidence, 1.0)

    def _generate_catalyst_rationale(self, catalyst_data: Dict,
                                   performance: Dict[str, float],
                                   reaction: ReactionPrediction) -> List[str]:
        """Generate rationale for catalyst recommendation."""
        rationale = []

        # Performance rationale
        if performance.get('activity', 0) > 0.7:
            rationale.append("High predicted activity based on electronic properties")

        if performance.get('chemoselectivity', 0) > 0.8:
            rationale.append("Excellent chemoselectivity expected")

        if performance.get('stability', 0) > 0.8:
            rationale.append("High stability under reaction conditions")

        # Literature precedent
        if reaction.reaction_type in catalyst_data.get('reactions', []):
            rationale.append(f"Proven effectiveness for {reaction.reaction_type} reactions")

        # Practical considerations
        if catalyst_data.get('cost') in ['low', 'moderate']:
            rationale.append("Cost-effective option")

        if catalyst_data.get('availability') == 'commercial':
            rationale.append("Commercially available")

        return rationale

    def _rank_catalysts(self, catalysts: List[CatalystRecommendation],
                       target_performance: Dict[str, float] = None) -> List[CatalystRecommendation]:
        """Rank catalysts by overall score."""
        def catalyst_score(catalyst):
            # Base score from confidence
            score = catalyst.confidence * 0.4

            # Performance score
            perf = catalyst.predicted_performance
            perf_score = (
                perf.get('activity', 0) * 0.3 +
                perf.get('chemoselectivity', 0) * 0.2 +
                perf.get('stability', 0) * 0.2 +
                perf.get('stereoselectivity', 0) * 0.15 +
                perf.get('regioselectivity', 0) * 0.15
            )
            score += perf_score * 0.4

            # Practical considerations
            cost_bonus = {'low': 0.1, 'moderate': 0.05, 'high': 0, 'very_high': -0.05}
            score += cost_bonus.get(catalyst.cost_estimate, 0)

            if catalyst.availability == 'commercial':
                score += 0.1

            # Target performance matching
            if target_performance:
                for metric, target in target_performance.items():
                    actual = perf.get(metric, 0)
                    if actual >= target:
                        score += 0.05
                    else:
                        score -= 0.02

            return score

        return sorted(catalysts, key=catalyst_score, reverse=True)

    def _generate_optimization_suggestions(self, catalysts: List[CatalystRecommendation],
                                         reaction: ReactionPrediction) -> List[str]:
        """Generate suggestions for catalyst optimization."""
        suggestions = []

        if not catalysts:
            suggestions.append("No suitable catalysts found - consider alternative reaction conditions")
            return suggestions

        best_catalyst = catalysts[0]

        # Performance-based suggestions
        if best_catalyst.predicted_performance.get('activity', 0) < 0.7:
            suggestions.append("Consider catalyst activation or higher loading")

        if best_catalyst.predicted_performance.get('chemoselectivity', 0) < 0.8:
            suggestions.append("Screen for more selective catalyst variants")

        if best_catalyst.predicted_performance.get('stability', 0) < 0.7:
            suggestions.append("Consider milder reaction conditions or catalyst stabilization")

        # Ligand optimization
        if 'transition_metal' in best_catalyst.catalyst_type:
            suggestions.append("Consider ligand screening for improved performance")

        # Condition optimization
        suggestions.append("Optimize catalyst loading (typically 1-10 mol%)")
        suggestions.append("Screen reaction temperature and solvent")

        return suggestions

    def _generate_screening_summary(self, catalysts: List[CatalystRecommendation],
                                  catalyst_types: List[str]) -> Dict[str, any]:
        """Generate summary of catalyst screening."""
        summary = {
            'total_catalysts_screened': len(catalysts),
            'catalyst_types_considered': catalyst_types,
            'top_catalyst_confidence': catalysts[0].confidence if catalysts else 0.0,
            'performance_distribution': {},
            'cost_distribution': {},
            'availability_summary': {}
        }

        if catalysts:
            # Performance distribution
            activities = [c.predicted_performance.get('activity', 0) for c in catalysts]
            summary['performance_distribution'] = {
                'mean_activity': np.mean(activities),
                'max_activity': np.max(activities),
                'min_activity': np.min(activities)
            }

            # Cost distribution
            costs = [c.cost_estimate for c in catalysts]
            summary['cost_distribution'] = {cost: costs.count(cost) for cost in set(costs)}

            # Availability summary
            availabilities = [c.availability for c in catalysts]
            summary['availability_summary'] = {avail: availabilities.count(avail) for avail in set(availabilities)}

        return summary

    def _display_catalyst_recommendations(self, screening_result: CatalystScreeningResult):
        """Display catalyst screening results."""
        print(f"\n🎯 CATALYST SCREENING RESULTS")
        print("-" * 50)

        summary = screening_result.screening_summary
        print(f"Catalysts screened: {summary['total_catalysts_screened']}")
        print(f"Catalyst types: {', '.join(summary['catalyst_types_considered'])}")

        if screening_result.top_catalysts:
            print(f"\n🏆 TOP CATALYST RECOMMENDATIONS")
            print("-" * 40)

            for i, catalyst in enumerate(screening_result.top_catalysts[:5], 1):
                print(f"\n{i}. {catalyst.catalyst_name} ({catalyst.catalyst_type})")
                print(f"   Confidence: {catalyst.confidence:.2f}")
                print(f"   Activity: {catalyst.predicted_performance.get('activity', 0):.2f}")
                print(f"   Selectivity: {catalyst.predicted_performance.get('chemoselectivity', 0):.2f}")
                print(f"   Cost: {catalyst.cost_estimate}")
                print(f"   Availability: {catalyst.availability}")

                if catalyst.rationale:
                    print(f"   Rationale: {catalyst.rationale[0]}")

        if screening_result.optimization_suggestions:
            print(f"\n💡 OPTIMIZATION SUGGESTIONS")
            print("-" * 30)
            for i, suggestion in enumerate(screening_result.optimization_suggestions[:3], 1):
                print(f"{i}. {suggestion}")

    def export_catalyst_screening(self, screening_result: CatalystScreeningResult, filename: str):
        """Export catalyst screening results to JSON."""
        export_data = {
            'reaction': asdict(screening_result.reaction),
            'top_catalysts': [asdict(cat) for cat in screening_result.top_catalysts],
            'screening_summary': screening_result.screening_summary,
            'optimization_suggestions': screening_result.optimization_suggestions
        }

        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)

        print(f"✅ Catalyst screening results exported to {filename}")

    def compare_catalysts(self, catalysts: List[CatalystRecommendation],
                         metrics: List[str] = None) -> Dict:
        """Compare multiple catalysts across different metrics."""
        if not metrics:
            metrics = ['activity', 'chemoselectivity', 'stability']

        comparison = {
            'catalysts': [cat.catalyst_name for cat in catalysts],
            'metrics': {},
            'rankings': {},
            'recommendations': []
        }

        for metric in metrics:
            values = [cat.predicted_performance.get(metric, 0) for cat in catalysts]
            comparison['metrics'][metric] = values

            # Rank by this metric
            ranked_indices = sorted(range(len(values)), key=lambda i: values[i], reverse=True)
            comparison['rankings'][metric] = [catalysts[i].catalyst_name for i in ranked_indices]

        # Generate comparison recommendations
        if catalysts:
            best_overall = catalysts[0]
            comparison['recommendations'].append(f"Overall best: {best_overall.catalyst_name}")

            for metric in metrics:
                best_for_metric = comparison['rankings'][metric][0]
                if best_for_metric != best_overall.catalyst_name:
                    comparison['recommendations'].append(
                        f"Best for {metric}: {best_for_metric}"
                    )

        return comparison
