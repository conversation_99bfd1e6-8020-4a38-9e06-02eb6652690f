"""
Reaction Condition Optimizer
Professional optimization engine for systematically optimizing reaction conditions
(temperature, pressure, concentration, time) for maximum yield and selectivity.
"""

import numpy as np
from scipy.optimize import minimize, differential_evolution, basinhopping
from typing import List, Dict, Tuple, Optional, Callable
from dataclasses import dataclass, asdict
import itertools
import json
from real_product_predictor import ReactionPrediction
from catalyst_design_system import CatalystRecommendation
from solvent_effect_predictor import SolventRecommendation

@dataclass
class ReactionConditions:
    """Complete set of reaction conditions."""
    temperature: float = 298.15  # Kelvin
    pressure: float = 1.0        # atm
    concentration: Dict[str, float] = None  # mol/L for each component
    time: float = 1.0            # hours
    catalyst_loading: float = 0.05  # mol%
    solvent: str = 'none'
    atmosphere: str = 'air'
    stirring_rate: float = 500.0  # rpm
    
    def __post_init__(self):
        if self.concentration is None:
            self.concentration = {}

@dataclass
class OptimizationObjective:
    """Optimization objective with weights."""
    name: str
    target_value: float
    weight: float = 1.0
    minimize: bool = False  # False = maximize, True = minimize

@dataclass
class ConditionConstraint:
    """Constraint on reaction conditions."""
    parameter: str
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    allowed_values: Optional[List] = None

@dataclass
class OptimizationResult:
    """Result of condition optimization."""
    optimal_conditions: ReactionConditions
    predicted_performance: Dict[str, float]
    optimization_history: List[Dict]
    convergence_info: Dict[str, any]
    sensitivity_analysis: Dict[str, float]
    recommendations: List[str]

class ReactionConditionOptimizer:
    """
    Professional reaction condition optimization system.
    
    Features:
    - Multi-objective optimization (yield, selectivity, rate, cost)
    - Multiple optimization algorithms (gradient-based, evolutionary, Bayesian)
    - Constraint handling for practical limitations
    - Sensitivity analysis and robustness assessment
    - Design of Experiments (DoE) integration
    """
    
    def __init__(self):
        """Initialize the condition optimizer."""
        self.performance_models = self._initialize_performance_models()
        self.optimization_algorithms = self._initialize_optimization_algorithms()
        self.constraint_handlers = self._initialize_constraint_handlers()
        
    def _initialize_performance_models(self) -> Dict:
        """Initialize models for predicting reaction performance."""
        return {
            'yield_model': self._create_yield_model(),
            'selectivity_model': self._create_selectivity_model(),
            'rate_model': self._create_rate_model(),
            'cost_model': self._create_cost_model(),
            'safety_model': self._create_safety_model()
        }
    
    def _create_yield_model(self):
        """Create model for predicting reaction yield."""
        class YieldModel:
            def predict_yield(self, reaction: ReactionPrediction, 
                            conditions: ReactionConditions) -> float:
                """Predict reaction yield based on conditions."""
                base_yield = 0.7  # Base yield assumption
                
                # Temperature effects (Arrhenius-like)
                T = conditions.temperature
                optimal_T = 350.0  # K, reaction-dependent
                
                if T < optimal_T:
                    # Lower temperature = lower rate but potentially higher selectivity
                    temp_factor = np.exp(-(optimal_T - T) / 50.0)
                else:
                    # Higher temperature = higher rate but potential decomposition
                    temp_factor = np.exp(-(T - optimal_T) / 100.0)
                
                base_yield *= temp_factor
                
                # Concentration effects
                total_conc = sum(conditions.concentration.values()) if conditions.concentration else 1.0
                if total_conc > 2.0:  # High concentration can lead to side reactions
                    conc_factor = 0.9
                elif total_conc < 0.1:  # Too dilute
                    conc_factor = 0.8
                else:
                    conc_factor = 1.0
                
                base_yield *= conc_factor
                
                # Time effects (with diminishing returns)
                time_factor = 1 - np.exp(-conditions.time / 2.0)  # Asymptotic approach
                base_yield *= time_factor
                
                # Catalyst effects
                if conditions.catalyst_loading > 0:
                    cat_factor = min(1.2, 1.0 + conditions.catalyst_loading * 2.0)
                    base_yield *= cat_factor
                
                return min(base_yield, 1.0)
        
        return YieldModel()
    
    def _create_selectivity_model(self):
        """Create model for predicting reaction selectivity."""
        class SelectivityModel:
            def predict_selectivity(self, reaction: ReactionPrediction,
                                   conditions: ReactionConditions) -> Dict[str, float]:
                """Predict different types of selectivity."""
                selectivities = {
                    'chemoselectivity': 0.8,
                    'regioselectivity': 0.7,
                    'stereoselectivity': 0.6
                }
                
                # Temperature effects on selectivity
                T = conditions.temperature
                if T > 400:  # High temperature reduces selectivity
                    temp_penalty = (T - 400) / 200.0
                    for key in selectivities:
                        selectivities[key] *= max(0.5, 1.0 - temp_penalty)
                elif T < 250:  # Very low temperature may be kinetically limiting
                    temp_penalty = (250 - T) / 100.0
                    for key in selectivities:
                        selectivities[key] *= max(0.7, 1.0 - temp_penalty * 0.3)
                
                # Concentration effects
                total_conc = sum(conditions.concentration.values()) if conditions.concentration else 1.0
                if total_conc > 1.5:  # High concentration favors side reactions
                    selectivities['chemoselectivity'] *= 0.9
                
                # Catalyst effects on selectivity
                if conditions.catalyst_loading > 0.1:  # High catalyst loading
                    selectivities['chemoselectivity'] *= 1.1
                    selectivities['stereoselectivity'] *= 1.05
                
                return {k: min(v, 1.0) for k, v in selectivities.items()}
        
        return SelectivityModel()
    
    def _create_rate_model(self):
        """Create model for predicting reaction rate."""
        class RateModel:
            def predict_rate_constant(self, reaction: ReactionPrediction,
                                     conditions: ReactionConditions) -> float:
                """Predict reaction rate constant."""
                # Arrhenius equation: k = A * exp(-Ea/RT)
                A = 1e10  # Pre-exponential factor (s^-1)
                Ea = reaction.activation_energy_estimate or 20.0  # kcal/mol
                R = 0.001987  # kcal/mol/K
                T = conditions.temperature
                
                k = A * np.exp(-Ea / (R * T))
                
                # Concentration effects (pseudo-first-order approximation)
                if conditions.concentration:
                    conc_factor = np.prod(list(conditions.concentration.values()))
                    k *= conc_factor
                
                # Catalyst effects
                if conditions.catalyst_loading > 0:
                    # Catalyst reduces activation energy
                    cat_factor = np.exp(conditions.catalyst_loading * 5.0 / (R * T))
                    k *= cat_factor
                
                # Pressure effects (for gas-phase reactions)
                if conditions.pressure != 1.0:
                    # Simplified pressure dependence
                    k *= conditions.pressure ** 0.5
                
                return k
        
        return RateModel()
    
    def _create_cost_model(self):
        """Create model for estimating reaction cost."""
        class CostModel:
            def estimate_cost(self, reaction: ReactionPrediction,
                            conditions: ReactionConditions) -> float:
                """Estimate relative reaction cost."""
                base_cost = 1.0
                
                # Temperature cost (heating/cooling)
                T = conditions.temperature
                if T > 350:
                    base_cost += (T - 350) / 100.0  # Heating cost
                elif T < 250:
                    base_cost += (250 - T) / 100.0  # Cooling cost
                
                # Pressure cost
                if conditions.pressure > 5.0:
                    base_cost += (conditions.pressure - 1.0) / 10.0
                
                # Time cost
                base_cost += conditions.time * 0.1
                
                # Catalyst cost
                if conditions.catalyst_loading > 0:
                    base_cost += conditions.catalyst_loading * 10.0  # Expensive catalysts
                
                # Solvent cost (simplified)
                expensive_solvents = ['dmso', 'dmf', 'acetonitrile']
                if conditions.solvent in expensive_solvents:
                    base_cost += 0.5
                
                return base_cost
        
        return CostModel()
    
    def _create_safety_model(self):
        """Create model for assessing reaction safety."""
        class SafetyModel:
            def assess_safety(self, reaction: ReactionPrediction,
                            conditions: ReactionConditions) -> float:
                """Assess reaction safety (0 = dangerous, 1 = safe)."""
                safety_score = 1.0
                
                # Temperature safety
                T = conditions.temperature
                if T > 500:  # Very high temperature
                    safety_score -= 0.3
                elif T > 400:
                    safety_score -= 0.1
                
                # Pressure safety
                if conditions.pressure > 10.0:
                    safety_score -= 0.2
                elif conditions.pressure > 5.0:
                    safety_score -= 0.1
                
                # Solvent safety
                dangerous_solvents = ['benzene', 'carbon_tetrachloride', 'ether']
                if conditions.solvent in dangerous_solvents:
                    safety_score -= 0.3
                
                # Concentration safety (runaway reactions)
                total_conc = sum(conditions.concentration.values()) if conditions.concentration else 1.0
                if total_conc > 3.0:
                    safety_score -= 0.2
                
                return max(safety_score, 0.0)
        
        return SafetyModel()
    
    def _initialize_optimization_algorithms(self) -> Dict:
        """Initialize different optimization algorithms."""
        return {
            'gradient_based': self._create_gradient_optimizer(),
            'evolutionary': self._create_evolutionary_optimizer(),
            'bayesian': self._create_bayesian_optimizer(),
            'grid_search': self._create_grid_search_optimizer()
        }
    
    def _create_gradient_optimizer(self):
        """Create gradient-based optimizer."""
        class GradientOptimizer:
            def optimize(self, objective_func: Callable, initial_conditions: ReactionConditions,
                        constraints: List[ConditionConstraint]) -> OptimizationResult:
                """Optimize using gradient-based methods."""
                # Convert conditions to parameter vector
                x0 = self._conditions_to_vector(initial_conditions)
                
                # Set up bounds from constraints
                bounds = self._constraints_to_bounds(constraints)
                
                # Optimize
                result = minimize(
                    lambda x: -objective_func(self._vector_to_conditions(x, initial_conditions)),
                    x0,
                    method='L-BFGS-B',
                    bounds=bounds,
                    options={'maxiter': 100}
                )
                
                optimal_conditions = self._vector_to_conditions(result.x, initial_conditions)
                
                return OptimizationResult(
                    optimal_conditions=optimal_conditions,
                    predicted_performance={'objective': -result.fun},
                    optimization_history=[],
                    convergence_info={'success': result.success, 'message': result.message},
                    sensitivity_analysis={},
                    recommendations=[]
                )
            
            def _conditions_to_vector(self, conditions: ReactionConditions) -> np.ndarray:
                """Convert conditions to optimization vector."""
                return np.array([
                    conditions.temperature,
                    conditions.pressure,
                    conditions.time,
                    conditions.catalyst_loading
                ])
            
            def _vector_to_conditions(self, x: np.ndarray, template: ReactionConditions) -> ReactionConditions:
                """Convert optimization vector to conditions."""
                return ReactionConditions(
                    temperature=x[0],
                    pressure=x[1],
                    concentration=template.concentration,
                    time=x[2],
                    catalyst_loading=x[3],
                    solvent=template.solvent,
                    atmosphere=template.atmosphere
                )
            
            def _constraints_to_bounds(self, constraints: List[ConditionConstraint]) -> List[Tuple]:
                """Convert constraints to optimization bounds."""
                bounds = [
                    (250.0, 500.0),  # Temperature bounds (K)
                    (0.1, 20.0),     # Pressure bounds (atm)
                    (0.1, 24.0),     # Time bounds (hours)
                    (0.0, 0.2)       # Catalyst loading bounds (mol fraction)
                ]
                
                # Apply specific constraints
                for constraint in constraints:
                    if constraint.parameter == 'temperature':
                        bounds[0] = (constraint.min_value or bounds[0][0], 
                                   constraint.max_value or bounds[0][1])
                    elif constraint.parameter == 'pressure':
                        bounds[1] = (constraint.min_value or bounds[1][0],
                                   constraint.max_value or bounds[1][1])
                    elif constraint.parameter == 'time':
                        bounds[2] = (constraint.min_value or bounds[2][0],
                                   constraint.max_value or bounds[2][1])
                    elif constraint.parameter == 'catalyst_loading':
                        bounds[3] = (constraint.min_value or bounds[3][0],
                                   constraint.max_value or bounds[3][1])
                
                return bounds
        
        return GradientOptimizer()
    
    def _create_evolutionary_optimizer(self):
        """Create evolutionary algorithm optimizer."""
        class EvolutionaryOptimizer:
            def optimize(self, objective_func: Callable, initial_conditions: ReactionConditions,
                        constraints: List[ConditionConstraint]) -> OptimizationResult:
                """Optimize using differential evolution."""
                bounds = self._get_parameter_bounds(constraints)
                
                result = differential_evolution(
                    lambda x: -objective_func(self._vector_to_conditions(x, initial_conditions)),
                    bounds,
                    maxiter=50,
                    popsize=15,
                    seed=42
                )
                
                optimal_conditions = self._vector_to_conditions(result.x, initial_conditions)
                
                return OptimizationResult(
                    optimal_conditions=optimal_conditions,
                    predicted_performance={'objective': -result.fun},
                    optimization_history=[],
                    convergence_info={'success': result.success, 'iterations': result.nit},
                    sensitivity_analysis={},
                    recommendations=[]
                )
            
            def _get_parameter_bounds(self, constraints: List[ConditionConstraint]) -> List[Tuple]:
                """Get parameter bounds for evolutionary algorithm."""
                return [(250.0, 500.0), (0.1, 20.0), (0.1, 24.0), (0.0, 0.2)]
            
            def _vector_to_conditions(self, x: np.ndarray, template: ReactionConditions) -> ReactionConditions:
                """Convert vector to conditions."""
                return ReactionConditions(
                    temperature=x[0],
                    pressure=x[1],
                    time=x[2],
                    catalyst_loading=x[3],
                    concentration=template.concentration,
                    solvent=template.solvent,
                    atmosphere=template.atmosphere
                )
        
        return EvolutionaryOptimizer()
    
    def _create_bayesian_optimizer(self):
        """Create Bayesian optimization algorithm."""
        class BayesianOptimizer:
            def optimize(self, objective_func: Callable, initial_conditions: ReactionConditions,
                        constraints: List[ConditionConstraint]) -> OptimizationResult:
                """Optimize using Bayesian optimization (simplified implementation)."""
                # Simplified Bayesian optimization using random sampling
                # In practice, would use libraries like scikit-optimize
                
                best_conditions = initial_conditions
                best_score = objective_func(initial_conditions)
                history = []
                
                # Random sampling with Gaussian process-like exploration
                for i in range(20):
                    # Generate candidate conditions
                    candidate = self._generate_candidate(initial_conditions, constraints)
                    score = objective_func(candidate)
                    
                    history.append({'conditions': asdict(candidate), 'score': score})
                    
                    if score > best_score:
                        best_score = score
                        best_conditions = candidate
                
                return OptimizationResult(
                    optimal_conditions=best_conditions,
                    predicted_performance={'objective': best_score},
                    optimization_history=history,
                    convergence_info={'success': True, 'iterations': 20},
                    sensitivity_analysis={},
                    recommendations=[]
                )
            
            def _generate_candidate(self, base_conditions: ReactionConditions,
                                  constraints: List[ConditionConstraint]) -> ReactionConditions:
                """Generate candidate conditions."""
                # Add Gaussian noise to base conditions
                return ReactionConditions(
                    temperature=max(250, min(500, base_conditions.temperature + np.random.normal(0, 20))),
                    pressure=max(0.1, min(20, base_conditions.pressure + np.random.normal(0, 1))),
                    time=max(0.1, min(24, base_conditions.time + np.random.normal(0, 2))),
                    catalyst_loading=max(0, min(0.2, base_conditions.catalyst_loading + np.random.normal(0, 0.02))),
                    concentration=base_conditions.concentration,
                    solvent=base_conditions.solvent,
                    atmosphere=base_conditions.atmosphere
                )
        
        return BayesianOptimizer()
    
    def _create_grid_search_optimizer(self):
        """Create grid search optimizer."""
        class GridSearchOptimizer:
            def optimize(self, objective_func: Callable, initial_conditions: ReactionConditions,
                        constraints: List[ConditionConstraint]) -> OptimizationResult:
                """Optimize using grid search."""
                # Define parameter grids
                temp_grid = np.linspace(250, 450, 5)
                pressure_grid = np.linspace(0.5, 5.0, 3)
                time_grid = np.linspace(0.5, 8.0, 4)
                cat_grid = np.linspace(0.01, 0.1, 3)
                
                best_conditions = initial_conditions
                best_score = -np.inf
                history = []
                
                for temp, press, time, cat in itertools.product(temp_grid, pressure_grid, time_grid, cat_grid):
                    candidate = ReactionConditions(
                        temperature=temp,
                        pressure=press,
                        time=time,
                        catalyst_loading=cat,
                        concentration=initial_conditions.concentration,
                        solvent=initial_conditions.solvent,
                        atmosphere=initial_conditions.atmosphere
                    )
                    
                    score = objective_func(candidate)
                    history.append({'conditions': asdict(candidate), 'score': score})
                    
                    if score > best_score:
                        best_score = score
                        best_conditions = candidate
                
                return OptimizationResult(
                    optimal_conditions=best_conditions,
                    predicted_performance={'objective': best_score},
                    optimization_history=history,
                    convergence_info={'success': True, 'evaluations': len(history)},
                    sensitivity_analysis={},
                    recommendations=[]
                )
        
        return GridSearchOptimizer()
    
    def _initialize_constraint_handlers(self) -> Dict:
        """Initialize constraint handling methods."""
        return {
            'penalty_method': self._create_penalty_handler(),
            'barrier_method': self._create_barrier_handler(),
            'feasibility_filter': self._create_feasibility_filter()
        }

    def optimize_reaction_conditions(self, reaction: ReactionPrediction,
                                   objectives: List[OptimizationObjective],
                                   initial_conditions: ReactionConditions = None,
                                   constraints: List[ConditionConstraint] = None,
                                   algorithm: str = 'evolutionary') -> OptimizationResult:
        """
        Optimize reaction conditions for multiple objectives.

        Args:
            reaction: The reaction to optimize
            objectives: List of optimization objectives
            initial_conditions: Starting point for optimization
            constraints: Constraints on reaction conditions
            algorithm: Optimization algorithm to use

        Returns:
            Optimization result with optimal conditions
        """
        print(f"\n🎯 REACTION CONDITION OPTIMIZATION")
        print("=" * 50)
        print(f"Reaction: {reaction.reaction_type}")
        print(f"Objectives: {[obj.name for obj in objectives]}")
        print(f"Algorithm: {algorithm}")

        # Set default initial conditions if not provided
        if initial_conditions is None:
            initial_conditions = ReactionConditions(
                temperature=298.15,
                pressure=1.0,
                time=2.0,
                catalyst_loading=0.05,
                concentration={'reactant': 1.0}
            )

        # Set default constraints if not provided
        if constraints is None:
            constraints = [
                ConditionConstraint('temperature', 250.0, 500.0),
                ConditionConstraint('pressure', 0.1, 20.0),
                ConditionConstraint('time', 0.1, 24.0),
                ConditionConstraint('catalyst_loading', 0.0, 0.2)
            ]

        # Create multi-objective function
        objective_function = self._create_multi_objective_function(reaction, objectives)

        # Select and run optimization algorithm
        optimizer = self.optimization_algorithms[algorithm]
        result = optimizer.optimize(objective_function, initial_conditions, constraints)

        # Perform sensitivity analysis
        sensitivity = self._perform_sensitivity_analysis(
            reaction, objectives, result.optimal_conditions
        )
        result.sensitivity_analysis = sensitivity

        # Generate recommendations
        recommendations = self._generate_optimization_recommendations(
            reaction, result, objectives
        )
        result.recommendations = recommendations

        # Display results
        self._display_optimization_results(result)

        return result

    def _create_multi_objective_function(self, reaction: ReactionPrediction,
                                       objectives: List[OptimizationObjective]) -> Callable:
        """Create multi-objective function for optimization."""
        def objective_function(conditions: ReactionConditions) -> float:
            total_score = 0.0

            for objective in objectives:
                if objective.name == 'yield':
                    value = self.performance_models['yield_model'].predict_yield(reaction, conditions)
                elif objective.name == 'selectivity':
                    selectivities = self.performance_models['selectivity_model'].predict_selectivity(reaction, conditions)
                    value = np.mean(list(selectivities.values()))
                elif objective.name == 'rate':
                    value = self.performance_models['rate_model'].predict_rate_constant(reaction, conditions)
                    value = min(1.0, value / 1e6)  # Normalize rate
                elif objective.name == 'cost':
                    value = self.performance_models['cost_model'].estimate_cost(reaction, conditions)
                    value = 1.0 / value  # Convert to benefit (lower cost = higher score)
                elif objective.name == 'safety':
                    value = self.performance_models['safety_model'].assess_safety(reaction, conditions)
                else:
                    value = 0.5  # Default value

                # Apply objective direction (minimize vs maximize)
                if objective.minimize:
                    value = 1.0 - value

                # Apply weight
                total_score += objective.weight * value

            return total_score

        return objective_function

    def _perform_sensitivity_analysis(self, reaction: ReactionPrediction,
                                    objectives: List[OptimizationObjective],
                                    optimal_conditions: ReactionConditions) -> Dict[str, float]:
        """Perform sensitivity analysis around optimal conditions."""
        sensitivity = {}

        # Parameters to analyze
        parameters = ['temperature', 'pressure', 'time', 'catalyst_loading']

        # Create objective function
        objective_func = self._create_multi_objective_function(reaction, objectives)
        baseline_score = objective_func(optimal_conditions)

        for param in parameters:
            # Calculate sensitivity by finite differences
            delta = 0.01  # 1% change

            # Create perturbed conditions
            perturbed_conditions = ReactionConditions(
                temperature=optimal_conditions.temperature,
                pressure=optimal_conditions.pressure,
                time=optimal_conditions.time,
                catalyst_loading=optimal_conditions.catalyst_loading,
                concentration=optimal_conditions.concentration,
                solvent=optimal_conditions.solvent,
                atmosphere=optimal_conditions.atmosphere
            )

            # Perturb the parameter
            current_value = getattr(optimal_conditions, param)
            setattr(perturbed_conditions, param, current_value * (1 + delta))

            # Calculate sensitivity
            perturbed_score = objective_func(perturbed_conditions)
            sensitivity[param] = (perturbed_score - baseline_score) / (delta * baseline_score)

        return sensitivity

    def _generate_optimization_recommendations(self, reaction: ReactionPrediction,
                                             result: OptimizationResult,
                                             objectives: List[OptimizationObjective]) -> List[str]:
        """Generate recommendations based on optimization results."""
        recommendations = []

        conditions = result.optimal_conditions

        # Temperature recommendations
        if conditions.temperature > 400:
            recommendations.append("High temperature - monitor for decomposition reactions")
        elif conditions.temperature < 273:
            recommendations.append("Low temperature - ensure adequate reaction rate")

        # Pressure recommendations
        if conditions.pressure > 5.0:
            recommendations.append("High pressure operation - ensure proper safety equipment")

        # Time recommendations
        if conditions.time > 12.0:
            recommendations.append("Long reaction time - consider process intensification")
        elif conditions.time < 0.5:
            recommendations.append("Short reaction time - verify complete conversion")

        # Catalyst recommendations
        if conditions.catalyst_loading > 0.1:
            recommendations.append("High catalyst loading - consider catalyst recovery")
        elif conditions.catalyst_loading < 0.01:
            recommendations.append("Low catalyst loading - verify catalytic activity")

        # Sensitivity-based recommendations
        sensitivity = result.sensitivity_analysis
        if sensitivity:
            most_sensitive = max(sensitivity.keys(), key=lambda k: abs(sensitivity[k]))
            recommendations.append(f"Most sensitive parameter: {most_sensitive} - control carefully")

        # Multi-objective recommendations
        if len(objectives) > 1:
            recommendations.append("Multi-objective optimization - consider trade-offs between objectives")

        return recommendations

    def _display_optimization_results(self, result: OptimizationResult):
        """Display optimization results."""
        print(f"\n🎯 OPTIMIZATION RESULTS")
        print("-" * 40)

        conditions = result.optimal_conditions
        print(f"Optimal Conditions:")
        print(f"  Temperature: {conditions.temperature:.1f} K ({conditions.temperature-273.15:.1f} °C)")
        print(f"  Pressure: {conditions.pressure:.2f} atm")
        print(f"  Time: {conditions.time:.2f} hours")
        print(f"  Catalyst loading: {conditions.catalyst_loading:.3f} mol%")

        if conditions.concentration:
            print(f"  Concentrations: {conditions.concentration}")

        print(f"\nPredicted Performance:")
        for metric, value in result.predicted_performance.items():
            print(f"  {metric}: {value:.3f}")

        print(f"\nConvergence Info:")
        for key, value in result.convergence_info.items():
            print(f"  {key}: {value}")

        if result.sensitivity_analysis:
            print(f"\nSensitivity Analysis:")
            for param, sensitivity in result.sensitivity_analysis.items():
                print(f"  {param}: {sensitivity:.3f}")

        if result.recommendations:
            print(f"\nRecommendations:")
            for i, rec in enumerate(result.recommendations, 1):
                print(f"  {i}. {rec}")

    def export_optimization_results(self, result: OptimizationResult, filename: str):
        """Export optimization results to JSON file."""
        export_data = {
            'optimal_conditions': asdict(result.optimal_conditions),
            'predicted_performance': result.predicted_performance,
            'optimization_history': result.optimization_history,
            'convergence_info': result.convergence_info,
            'sensitivity_analysis': result.sensitivity_analysis,
            'recommendations': result.recommendations
        }

        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)

        print(f"✅ Optimization results exported to {filename}")

    def _create_penalty_handler(self):
        """Create penalty method for constraint handling."""
        class PenaltyHandler:
            def apply_penalty(self, objective_value: float, conditions: ReactionConditions,
                            constraints: List[ConditionConstraint]) -> float:
                """Apply penalty for constraint violations."""
                penalty = 0.0

                for constraint in constraints:
                    value = getattr(conditions, constraint.parameter, 0)

                    if constraint.min_value is not None and value < constraint.min_value:
                        penalty += (constraint.min_value - value) ** 2

                    if constraint.max_value is not None and value > constraint.max_value:
                        penalty += (value - constraint.max_value) ** 2

                return objective_value - 1000 * penalty  # Large penalty factor

        return PenaltyHandler()

    def _create_barrier_handler(self):
        """Create barrier method for constraint handling."""
        class BarrierHandler:
            def apply_barrier(self, objective_value: float, conditions: ReactionConditions,
                            constraints: List[ConditionConstraint]) -> float:
                """Apply barrier function for constraint handling."""
                barrier = 0.0

                for constraint in constraints:
                    value = getattr(conditions, constraint.parameter, 0)

                    if constraint.min_value is not None:
                        if value <= constraint.min_value:
                            return -np.inf  # Infeasible
                        barrier += -np.log(value - constraint.min_value)

                    if constraint.max_value is not None:
                        if value >= constraint.max_value:
                            return -np.inf  # Infeasible
                        barrier += -np.log(constraint.max_value - value)

                return objective_value - 0.1 * barrier  # Barrier coefficient

        return BarrierHandler()

    def _create_feasibility_filter(self):
        """Create feasibility filter for constraint handling."""
        class FeasibilityFilter:
            def is_feasible(self, conditions: ReactionConditions,
                          constraints: List[ConditionConstraint]) -> bool:
                """Check if conditions satisfy all constraints."""
                for constraint in constraints:
                    value = getattr(conditions, constraint.parameter, 0)

                    if constraint.min_value is not None and value < constraint.min_value:
                        return False

                    if constraint.max_value is not None and value > constraint.max_value:
                        return False

                    if constraint.allowed_values is not None and value not in constraint.allowed_values:
                        return False

                return True

        return FeasibilityFilter()
