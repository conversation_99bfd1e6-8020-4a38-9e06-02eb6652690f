"""
Comprehensive Solvent Effect Predictor
Professional system for predicting and optimizing solvent effects on reactions.
Accounts for implicit and explicit solvation effects on reaction outcomes.
"""

import numpy as np
from rdkit import Chem
from rdkit.Chem import Descriptors, rdMolDescriptors
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass, asdict
import json
import math
from real_product_predictor import ReactionPrediction

@dataclass
class SolventProperties:
    """Comprehensive solvent properties and descriptors."""
    name: str
    smiles: str
    # Physical properties
    dielectric_constant: float = 0.0
    dipole_moment: float = 0.0
    refractive_index: float = 0.0
    viscosity: float = 0.0
    surface_tension: float = 0.0
    
    # Solvatochromic parameters
    alpha: float = 0.0  # Hydrogen bond acidity
    beta: float = 0.0   # Hydrogen bond basicity
    pi_star: float = 0.0  # Dipolarity/polarizability
    
    # Hansen solubility parameters
    delta_d: float = 0.0  # Dispersion
    delta_p: float = 0.0  # Polar
    delta_h: float = 0.0  # Hydrogen bonding
    
    # Derived properties
    polarity_index: float = 0.0
    protic_index: float = 0.0
    coordinating_ability: float = 0.0

@dataclass
class SolvationEffect:
    """Quantified solvation effects on reaction parameters."""
    solvent: str
    # Thermodynamic effects
    delta_g_solvation: float = 0.0  # kcal/mol
    delta_h_solvation: float = 0.0  # kcal/mol
    delta_s_solvation: float = 0.0  # cal/mol·K
    
    # Kinetic effects
    rate_enhancement: float = 1.0  # Relative to gas phase
    activation_energy_change: float = 0.0  # kcal/mol
    
    # Selectivity effects
    chemoselectivity_factor: float = 1.0
    regioselectivity_factor: float = 1.0
    stereoselectivity_factor: float = 1.0
    
    # Mechanistic effects
    mechanism_change_probability: float = 0.0
    ionic_character_enhancement: float = 0.0

@dataclass
class SolventRecommendation:
    """Solvent recommendation with detailed analysis."""
    solvent_name: str
    solvent_smiles: str
    properties: SolventProperties
    predicted_effects: SolvationEffect
    suitability_score: float
    rationale: List[str]
    practical_considerations: Dict[str, str]
    alternative_solvents: List[str]

class ComprehensiveSolventPredictor:
    """
    Professional solvent effect prediction system.
    
    Features:
    - Comprehensive solvent database with properties
    - Implicit solvation models (PCM, SMD)
    - Explicit solvation effects
    - Solvent-dependent reaction mechanism analysis
    - Multi-objective solvent optimization
    """
    
    def __init__(self):
        """Initialize the solvent effect predictor."""
        self.solvent_database = self._load_comprehensive_solvent_database()
        self.solvation_models = self._initialize_solvation_models()
        self.reaction_solvent_effects = self._load_reaction_solvent_effects()
        
    def _load_comprehensive_solvent_database(self) -> Dict[str, SolventProperties]:
        """Load comprehensive database of solvents with properties."""
        solvents = {}
        
        # Protic solvents
        solvents['water'] = SolventProperties(
            name='Water', smiles='O',
            dielectric_constant=80.1, dipole_moment=1.85, refractive_index=1.333,
            alpha=1.17, beta=0.47, pi_star=1.09,
            delta_d=15.5, delta_p=16.0, delta_h=42.3,
            polarity_index=10.2, protic_index=1.0, coordinating_ability=0.8
        )
        
        solvents['methanol'] = SolventProperties(
            name='Methanol', smiles='CO',
            dielectric_constant=32.7, dipole_moment=1.70, refractive_index=1.329,
            alpha=0.98, beta=0.66, pi_star=0.60,
            delta_d=15.1, delta_p=12.3, delta_h=22.3,
            polarity_index=5.1, protic_index=0.9, coordinating_ability=0.7
        )
        
        solvents['ethanol'] = SolventProperties(
            name='Ethanol', smiles='CCO',
            dielectric_constant=24.5, dipole_moment=1.69, refractive_index=1.361,
            alpha=0.86, beta=0.75, pi_star=0.54,
            delta_d=15.8, delta_p=8.8, delta_h=19.4,
            polarity_index=4.3, protic_index=0.8, coordinating_ability=0.6
        )
        
        # Polar aprotic solvents
        solvents['dmso'] = SolventProperties(
            name='DMSO', smiles='CS(=O)C',
            dielectric_constant=46.7, dipole_moment=3.96, refractive_index=1.479,
            alpha=0.00, beta=0.76, pi_star=1.00,
            delta_d=18.4, delta_p=16.4, delta_h=10.2,
            polarity_index=7.2, protic_index=0.0, coordinating_ability=0.9
        )
        
        solvents['dmf'] = SolventProperties(
            name='DMF', smiles='CN(C)C=O',
            dielectric_constant=36.7, dipole_moment=3.82, refractive_index=1.430,
            alpha=0.00, beta=0.69, pi_star=0.88,
            delta_d=17.4, delta_p=13.7, delta_h=11.3,
            polarity_index=6.4, protic_index=0.0, coordinating_ability=0.8
        )
        
        solvents['acetonitrile'] = SolventProperties(
            name='Acetonitrile', smiles='CC#N',
            dielectric_constant=37.5, dipole_moment=3.92, refractive_index=1.344,
            alpha=0.19, beta=0.31, pi_star=0.75,
            delta_d=15.3, delta_p=18.0, delta_h=6.1,
            polarity_index=5.8, protic_index=0.1, coordinating_ability=0.4
        )
        
        # Nonpolar solvents
        solvents['toluene'] = SolventProperties(
            name='Toluene', smiles='Cc1ccccc1',
            dielectric_constant=2.38, dipole_moment=0.36, refractive_index=1.497,
            alpha=0.00, beta=0.11, pi_star=0.54,
            delta_d=18.0, delta_p=1.4, delta_h=2.0,
            polarity_index=2.4, protic_index=0.0, coordinating_ability=0.1
        )
        
        solvents['hexane'] = SolventProperties(
            name='Hexane', smiles='CCCCCC',
            dielectric_constant=1.88, dipole_moment=0.00, refractive_index=1.375,
            alpha=0.00, beta=0.00, pi_star=0.00,
            delta_d=14.9, delta_p=0.0, delta_h=0.0,
            polarity_index=0.1, protic_index=0.0, coordinating_ability=0.0
        )
        
        solvents['dichloromethane'] = SolventProperties(
            name='Dichloromethane', smiles='ClCCl',
            dielectric_constant=8.93, dipole_moment=1.60, refractive_index=1.424,
            alpha=0.13, beta=0.10, pi_star=0.82,
            delta_d=18.2, delta_p=6.3, delta_h=6.1,
            polarity_index=3.1, protic_index=0.0, coordinating_ability=0.2
        )
        
        # Ether solvents
        solvents['thf'] = SolventProperties(
            name='THF', smiles='C1CCOC1',
            dielectric_constant=7.58, dipole_moment=1.63, refractive_index=1.407,
            alpha=0.00, beta=0.55, pi_star=0.58,
            delta_d=16.8, delta_p=5.7, delta_h=8.0,
            polarity_index=4.0, protic_index=0.0, coordinating_ability=0.6
        )
        
        solvents['diethyl_ether'] = SolventProperties(
            name='Diethyl ether', smiles='CCOCC',
            dielectric_constant=4.33, dipole_moment=1.15, refractive_index=1.353,
            alpha=0.00, beta=0.47, pi_star=0.27,
            delta_d=14.5, delta_p=2.9, delta_h=4.6,
            polarity_index=2.8, protic_index=0.0, coordinating_ability=0.5
        )
        
        return solvents
    
    def _initialize_solvation_models(self) -> Dict:
        """Initialize different solvation models."""
        return {
            'pcm_model': self._create_pcm_model(),
            'smd_model': self._create_smd_model(),
            'explicit_model': self._create_explicit_solvation_model(),
            'hybrid_model': self._create_hybrid_model()
        }
    
    def _create_pcm_model(self):
        """Create Polarizable Continuum Model."""
        class PCMModel:
            def calculate_solvation_energy(self, solute_properties: Dict, 
                                         solvent_properties: SolventProperties) -> float:
                """Calculate solvation energy using PCM approach."""
                # Simplified PCM calculation
                dielectric = solvent_properties.dielectric_constant
                
                # Born solvation energy (simplified)
                if 'charge' in solute_properties:
                    charge = solute_properties['charge']
                    radius = solute_properties.get('radius', 3.0)  # Angstrom
                    
                    # Born equation: ΔG = -q²/(8πε₀) * (1 - 1/ε) / r
                    born_energy = -charge**2 * 332.0 * (1 - 1/dielectric) / radius
                    return born_energy
                
                return 0.0
            
            def calculate_reaction_field_effects(self, reaction_properties: Dict,
                                               solvent_properties: SolventProperties) -> Dict:
                """Calculate reaction field effects on reaction parameters."""
                effects = {}
                
                # Dielectric effect on activation energy
                dielectric = solvent_properties.dielectric_constant
                if dielectric > 10:  # Polar solvent
                    effects['activation_energy_change'] = -2.0  # kcal/mol stabilization
                elif dielectric < 5:  # Nonpolar solvent
                    effects['activation_energy_change'] = 1.0
                else:
                    effects['activation_energy_change'] = 0.0
                
                return effects
        
        return PCMModel()
    
    def _create_smd_model(self):
        """Create Solvation Model based on Density (SMD)."""
        class SMDModel:
            def calculate_solvation_free_energy(self, solute_properties: Dict,
                                              solvent_properties: SolventProperties) -> float:
                """Calculate solvation free energy using SMD model."""
                # Simplified SMD calculation
                # ΔG_solv = ΔG_ENP + ΔG_CDS
                
                # Electrostatic-nonpolar-polarization term
                delta_g_enp = self._calculate_enp_term(solute_properties, solvent_properties)
                
                # Cavitation-dispersion-solvent_structure term
                delta_g_cds = self._calculate_cds_term(solute_properties, solvent_properties)
                
                return delta_g_enp + delta_g_cds
            
            def _calculate_enp_term(self, solute_props: Dict, solvent_props: SolventProperties) -> float:
                """Calculate ENP term."""
                # Simplified calculation based on dielectric constant
                dielectric = solvent_props.dielectric_constant
                charge_density = solute_props.get('charge_density', 0.0)
                
                return -charge_density * math.log(dielectric) * 2.0
            
            def _calculate_cds_term(self, solute_props: Dict, solvent_props: SolventProperties) -> float:
                """Calculate CDS term."""
                # Simplified calculation based on surface area and solvent properties
                surface_area = solute_props.get('surface_area', 100.0)  # Ų
                
                # Surface tension contribution
                gamma = solvent_props.surface_tension if hasattr(solvent_props, 'surface_tension') else 20.0
                
                return gamma * surface_area * 0.001  # Convert to kcal/mol
        
        return SMDModel()
    
    def _create_explicit_solvation_model(self):
        """Create explicit solvation model."""
        class ExplicitSolvationModel:
            def calculate_explicit_effects(self, reaction: ReactionPrediction,
                                         solvent_properties: SolventProperties) -> Dict:
                """Calculate explicit solvation effects."""
                effects = {}
                
                # Hydrogen bonding effects
                if solvent_properties.protic_index > 0.5:
                    effects['hydrogen_bonding_stabilization'] = -1.5  # kcal/mol
                
                # Coordination effects
                if solvent_properties.coordinating_ability > 0.5:
                    effects['coordination_stabilization'] = -1.0
                
                # Specific solvent-solute interactions
                effects['specific_interactions'] = self._calculate_specific_interactions(
                    reaction, solvent_properties
                )
                
                return effects
            
            def _calculate_specific_interactions(self, reaction: ReactionPrediction,
                                               solvent_props: SolventProperties) -> float:
                """Calculate specific solvent-solute interactions."""
                interaction_energy = 0.0
                
                # Check for specific interactions based on reaction type
                if 'sn2' in reaction.reaction_type.lower():
                    if solvent_props.coordinating_ability > 0.7:
                        interaction_energy -= 2.0  # Strong coordination stabilizes TS
                
                if 'elimination' in reaction.reaction_type.lower():
                    if solvent_props.protic_index > 0.8:
                        interaction_energy += 1.0  # Protic solvents can interfere
                
                return interaction_energy
        
        return ExplicitSolvationModel()
    
    def _create_hybrid_model(self):
        """Create hybrid implicit/explicit model."""
        class HybridModel:
            def __init__(self, pcm_model, explicit_model):
                self.pcm_model = pcm_model
                self.explicit_model = explicit_model
            
            def calculate_total_solvation_effects(self, reaction: ReactionPrediction,
                                                solvent_properties: SolventProperties) -> SolvationEffect:
                """Calculate total solvation effects using hybrid approach."""
                # Get implicit effects
                solute_props = {'charge': 0, 'radius': 3.0, 'surface_area': 100.0}
                implicit_energy = self.pcm_model.calculate_solvation_energy(
                    solute_props, solvent_properties
                )
                
                # Get explicit effects
                explicit_effects = self.explicit_model.calculate_explicit_effects(
                    reaction, solvent_properties
                )
                
                # Combine effects
                total_delta_g = implicit_energy + sum(explicit_effects.values())
                
                # Estimate rate enhancement
                rate_enhancement = math.exp(-total_delta_g / (0.593))  # RT at 298K
                
                return SolvationEffect(
                    solvent=solvent_properties.name,
                    delta_g_solvation=total_delta_g,
                    rate_enhancement=rate_enhancement,
                    activation_energy_change=total_delta_g * 0.5  # Rough estimate
                )
        
        return HybridModel(self.solvation_models['pcm_model'], 
                          self.solvation_models['explicit_model'])
    
    def _load_reaction_solvent_effects(self) -> Dict[str, Dict]:
        """Load known reaction-specific solvent effects."""
        return {
            'sn2_substitution': {
                'polar_aprotic_preferred': True,
                'protic_penalty': -2.0,  # kcal/mol penalty for protic solvents
                'dielectric_optimum': (20, 40),
                'coordinating_bonus': 1.5
            },
            'sn1_substitution': {
                'polar_protic_preferred': True,
                'protic_bonus': 2.0,
                'dielectric_optimum': (30, 80),
                'coordinating_penalty': -1.0
            },
            'elimination': {
                'base_strength_dependent': True,
                'protic_variable': True,
                'temperature_sensitive': True
            },
            'esterification': {
                'protic_catalytic': True,
                'water_removal_important': True,
                'nonpolar_preferred': False
            },
            'diels_alder': {
                'water_acceleration': True,
                'polar_rate_enhancement': 2.0,
                'pressure_effects': True
            }
        }

    def predict_optimal_solvent(self, reaction: ReactionPrediction,
                              objectives: Dict[str, float] = None,
                              constraints: Dict[str, any] = None) -> List[SolventRecommendation]:
        """
        Predict optimal solvent for a reaction with multi-objective optimization.

        Args:
            reaction: The reaction to optimize
            objectives: Optimization objectives (rate, selectivity, etc.)
            constraints: Practical constraints (cost, toxicity, etc.)

        Returns:
            List of solvent recommendations ranked by suitability
        """
        print(f"\n🧪 COMPREHENSIVE SOLVENT OPTIMIZATION")
        print("=" * 50)
        print(f"Reaction: {reaction.reaction_type}")
        print(f"Description: {reaction.description}")

        if objectives:
            print(f"Objectives: {objectives}")
        if constraints:
            print(f"Constraints: {constraints}")

        # Screen all solvents in database
        solvent_recommendations = []

        for solvent_name, solvent_props in self.solvent_database.items():
            print(f"\nEvaluating {solvent_name}...")

            # Apply constraints first
            if constraints and not self._meets_solvent_constraints(solvent_props, constraints):
                print(f"   ❌ Fails constraints")
                continue

            # Calculate solvation effects
            solvation_effects = self._calculate_comprehensive_solvation_effects(
                reaction, solvent_props
            )

            # Calculate suitability score
            suitability_score = self._calculate_solvent_suitability(
                reaction, solvent_props, solvation_effects, objectives
            )

            # Generate rationale
            rationale = self._generate_solvent_rationale(
                reaction, solvent_props, solvation_effects
            )

            # Get practical considerations
            practical_considerations = self._get_practical_considerations(solvent_props)

            # Find alternative solvents
            alternatives = self._find_alternative_solvents(solvent_props)

            recommendation = SolventRecommendation(
                solvent_name=solvent_name,
                solvent_smiles=solvent_props.smiles,
                properties=solvent_props,
                predicted_effects=solvation_effects,
                suitability_score=suitability_score,
                rationale=rationale,
                practical_considerations=practical_considerations,
                alternative_solvents=alternatives
            )

            solvent_recommendations.append(recommendation)
            print(f"   ✅ Score: {suitability_score:.2f}")

        # Rank recommendations
        ranked_recommendations = sorted(
            solvent_recommendations,
            key=lambda x: x.suitability_score,
            reverse=True
        )

        # Display results
        self._display_solvent_recommendations(ranked_recommendations[:5])

        return ranked_recommendations

    def _meets_solvent_constraints(self, solvent_props: SolventProperties,
                                 constraints: Dict[str, any]) -> bool:
        """Check if solvent meets practical constraints."""
        # Toxicity constraints
        if 'max_toxicity' in constraints:
            # Simplified toxicity assessment
            toxic_solvents = ['benzene', 'carbon_tetrachloride', 'chloroform']
            if solvent_props.name.lower() in toxic_solvents:
                return False

        # Environmental constraints
        if 'green_chemistry' in constraints and constraints['green_chemistry']:
            green_solvents = ['water', 'ethanol', 'ethyl_acetate']
            if solvent_props.name.lower() not in green_solvents:
                return False

        # Polarity constraints
        if 'polarity_range' in constraints:
            min_pol, max_pol = constraints['polarity_range']
            if not (min_pol <= solvent_props.polarity_index <= max_pol):
                return False

        # Protic/aprotic constraints
        if 'protic_required' in constraints:
            required = constraints['protic_required']
            is_protic = solvent_props.protic_index > 0.5
            if required != is_protic:
                return False

        return True

    def _calculate_comprehensive_solvation_effects(self, reaction: ReactionPrediction,
                                                 solvent_props: SolventProperties) -> SolvationEffect:
        """Calculate comprehensive solvation effects using all models."""
        # Use hybrid model for most accurate prediction
        hybrid_model = self.solvation_models['hybrid_model']
        base_effects = hybrid_model.calculate_total_solvation_effects(reaction, solvent_props)

        # Add reaction-specific effects
        reaction_effects = self._get_reaction_specific_effects(reaction, solvent_props)

        # Combine effects
        total_effects = SolvationEffect(
            solvent=solvent_props.name,
            delta_g_solvation=base_effects.delta_g_solvation + reaction_effects.get('delta_g_bonus', 0),
            rate_enhancement=base_effects.rate_enhancement * reaction_effects.get('rate_factor', 1.0),
            activation_energy_change=base_effects.activation_energy_change + reaction_effects.get('ea_change', 0),
            chemoselectivity_factor=reaction_effects.get('chemoselectivity', 1.0),
            regioselectivity_factor=reaction_effects.get('regioselectivity', 1.0),
            stereoselectivity_factor=reaction_effects.get('stereoselectivity', 1.0)
        )

        return total_effects

    def _get_reaction_specific_effects(self, reaction: ReactionPrediction,
                                     solvent_props: SolventProperties) -> Dict[str, float]:
        """Get reaction-specific solvent effects."""
        reaction_type = reaction.reaction_type.lower()
        effects = {}

        if reaction_type in self.reaction_solvent_effects:
            reaction_data = self.reaction_solvent_effects[reaction_type]

            # SN2 reactions
            if reaction_type == 'sn2_substitution':
                if solvent_props.protic_index > 0.5:
                    effects['delta_g_bonus'] = reaction_data['protic_penalty']
                    effects['rate_factor'] = 0.1
                elif solvent_props.coordinating_ability > 0.5:
                    effects['delta_g_bonus'] = reaction_data['coordinating_bonus']
                    effects['rate_factor'] = 3.0

            # SN1 reactions
            elif reaction_type == 'sn1_substitution':
                if solvent_props.protic_index > 0.5:
                    effects['delta_g_bonus'] = reaction_data['protic_bonus']
                    effects['rate_factor'] = 5.0

            # Diels-Alder reactions
            elif reaction_type == 'diels_alder':
                if solvent_props.name == 'water':
                    effects['rate_factor'] = 10.0  # Water acceleration
                elif solvent_props.polarity_index > 5:
                    effects['rate_factor'] = reaction_data['polar_rate_enhancement']

        # Default effects if not specified
        if not effects:
            effects = {'delta_g_bonus': 0.0, 'rate_factor': 1.0, 'ea_change': 0.0}

        return effects

    def _calculate_solvent_suitability(self, reaction: ReactionPrediction,
                                     solvent_props: SolventProperties,
                                     solvation_effects: SolvationEffect,
                                     objectives: Dict[str, float] = None) -> float:
        """Calculate overall solvent suitability score."""
        base_score = 0.5

        # Rate enhancement contribution
        if solvation_effects.rate_enhancement > 1.0:
            base_score += min(0.3, math.log(solvation_effects.rate_enhancement) * 0.1)
        else:
            base_score -= 0.2

        # Selectivity contributions
        selectivity_avg = (
            solvation_effects.chemoselectivity_factor +
            solvation_effects.regioselectivity_factor +
            solvation_effects.stereoselectivity_factor
        ) / 3.0

        if selectivity_avg > 1.0:
            base_score += min(0.2, (selectivity_avg - 1.0) * 0.5)

        # Thermodynamic favorability
        if solvation_effects.delta_g_solvation < 0:
            base_score += min(0.2, abs(solvation_effects.delta_g_solvation) * 0.05)

        # Objective-specific scoring
        if objectives:
            for objective, weight in objectives.items():
                if objective == 'rate' and solvation_effects.rate_enhancement > 1.5:
                    base_score += weight * 0.1
                elif objective == 'selectivity' and selectivity_avg > 1.2:
                    base_score += weight * 0.1
                elif objective == 'green_chemistry' and solvent_props.name in ['water', 'ethanol']:
                    base_score += weight * 0.2

        return min(base_score, 1.0)

    def _generate_solvent_rationale(self, reaction: ReactionPrediction,
                                  solvent_props: SolventProperties,
                                  solvation_effects: SolvationEffect) -> List[str]:
        """Generate rationale for solvent recommendation."""
        rationale = []

        # Rate effects
        if solvation_effects.rate_enhancement > 2.0:
            rationale.append(f"Significant rate enhancement ({solvation_effects.rate_enhancement:.1f}x)")
        elif solvation_effects.rate_enhancement < 0.5:
            rationale.append(f"Rate reduction expected ({solvation_effects.rate_enhancement:.1f}x)")

        # Thermodynamic effects
        if solvation_effects.delta_g_solvation < -2.0:
            rationale.append("Strong thermodynamic stabilization")
        elif solvation_effects.delta_g_solvation > 2.0:
            rationale.append("Thermodynamic destabilization")

        # Solvent-specific effects
        if solvent_props.protic_index > 0.8:
            rationale.append("Strong hydrogen bonding capability")

        if solvent_props.coordinating_ability > 0.7:
            rationale.append("Good coordinating solvent for metal catalysis")

        if solvent_props.polarity_index > 7:
            rationale.append("High polarity suitable for ionic reactions")

        # Reaction-specific rationale
        reaction_type = reaction.reaction_type.lower()
        if 'sn2' in reaction_type and solvent_props.coordinating_ability > 0.5:
            rationale.append("Polar aprotic nature favors SN2 mechanism")

        if 'diels_alder' in reaction_type and solvent_props.name == 'water':
            rationale.append("Water provides unique acceleration for Diels-Alder reactions")

        return rationale

    def _get_practical_considerations(self, solvent_props: SolventProperties) -> Dict[str, str]:
        """Get practical considerations for solvent use."""
        considerations = {}

        # Safety considerations
        if solvent_props.name in ['diethyl_ether', 'thf']:
            considerations['safety'] = 'Peroxide formation risk - use stabilizers'
        elif solvent_props.name in ['dichloromethane', 'chloroform']:
            considerations['safety'] = 'Potential carcinogen - use proper ventilation'

        # Handling considerations
        if solvent_props.name == 'water':
            considerations['handling'] = 'May require inert atmosphere for sensitive reactions'
        elif solvent_props.name in ['dmso', 'dmf']:
            considerations['handling'] = 'High boiling point - difficult to remove'

        # Cost considerations
        expensive_solvents = ['acetonitrile', 'dmf', 'dmso']
        if solvent_props.name in expensive_solvents:
            considerations['cost'] = 'Relatively expensive solvent'
        else:
            considerations['cost'] = 'Cost-effective option'

        # Environmental considerations
        green_solvents = ['water', 'ethanol', 'ethyl_acetate']
        if solvent_props.name in green_solvents:
            considerations['environmental'] = 'Green chemistry compatible'
        else:
            considerations['environmental'] = 'Consider environmental impact'

        return considerations

    def _find_alternative_solvents(self, target_solvent: SolventProperties) -> List[str]:
        """Find alternative solvents with similar properties."""
        alternatives = []

        for name, props in self.solvent_database.items():
            if name == target_solvent.name:
                continue

            # Calculate property similarity
            similarity = self._calculate_solvent_similarity(target_solvent, props)

            if similarity > 0.7:  # High similarity threshold
                alternatives.append(name)

        return alternatives[:3]  # Top 3 alternatives

    def _calculate_solvent_similarity(self, solvent1: SolventProperties,
                                    solvent2: SolventProperties) -> float:
        """Calculate similarity between two solvents."""
        # Compare key properties
        properties = [
            'dielectric_constant', 'polarity_index', 'protic_index',
            'coordinating_ability', 'alpha', 'beta', 'pi_star'
        ]

        similarities = []

        for prop in properties:
            val1 = getattr(solvent1, prop, 0)
            val2 = getattr(solvent2, prop, 0)

            if val1 == 0 and val2 == 0:
                similarities.append(1.0)
            elif val1 == 0 or val2 == 0:
                similarities.append(0.0)
            else:
                # Normalized difference
                diff = abs(val1 - val2) / max(val1, val2)
                similarities.append(1.0 - diff)

        return np.mean(similarities)

    def _display_solvent_recommendations(self, recommendations: List[SolventRecommendation]):
        """Display solvent recommendations."""
        print(f"\n🎯 SOLVENT RECOMMENDATIONS")
        print("-" * 50)

        for i, rec in enumerate(recommendations, 1):
            print(f"\n{i}. {rec.solvent_name.upper()}")
            print(f"   Suitability Score: {rec.suitability_score:.2f}")
            print(f"   Rate Enhancement: {rec.predicted_effects.rate_enhancement:.1f}x")
            print(f"   ΔG Solvation: {rec.predicted_effects.delta_g_solvation:.1f} kcal/mol")

            if rec.rationale:
                print(f"   Key Benefits: {rec.rationale[0]}")

            if rec.practical_considerations:
                safety = rec.practical_considerations.get('safety', 'Standard precautions')
                print(f"   Safety: {safety}")

            if rec.alternative_solvents:
                print(f"   Alternatives: {', '.join(rec.alternative_solvents)}")

    def export_solvent_analysis(self, recommendations: List[SolventRecommendation],
                              filename: str):
        """Export solvent analysis to JSON file."""
        export_data = {
            'recommendations': [asdict(rec) for rec in recommendations],
            'analysis_summary': {
                'total_solvents_evaluated': len(recommendations),
                'top_recommendation': recommendations[0].solvent_name if recommendations else None,
                'best_score': recommendations[0].suitability_score if recommendations else 0.0
            }
        }

        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)

        print(f"✅ Solvent analysis exported to {filename}")
