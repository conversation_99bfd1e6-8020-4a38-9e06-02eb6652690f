#!/usr/bin/env python3
"""
Comprehensive Reaction Validation System
Addresses the three main problems:
1. Bad ML predictions
2. Atom imbalance  
3. Model hallucination
"""

from rdkit import Chem
from rdkit.Chem import rdMolDescriptors
from collections import Counter
import json
from typing import List, Dict, Tuple, Optional

class ReactionValidator:
    """Comprehensive validation system for AI-predicted reactions"""
    
    def __init__(self):
        # Known reaction templates for cross-validation
        self.reaction_templates = {
            'diels_alder': {
                'pattern': 'diene + dienophile -> cyclic_adduct',
                'atom_change': 'no_change',
                'bond_change': 'cyclization',
                'examples': ['C1=CC=CC1 + O=C1OC(=O)C=C1 -> bicyclic']
            },
            'sn2': {
                'pattern': 'R-X + Nu -> R-Nu + X',
                'atom_change': 'no_change',
                'bond_change': 'substitution',
                'examples': ['CCCCBr + CN -> CCCCCN + Br']
            },
            'esterification': {
                'pattern': 'RCOOH + ROH -> RCOOR + H2O',
                'atom_change': 'water_elimination',
                'bond_change': 'ester_formation',
                'examples': ['CC(=O)O + CCO -> CC(=O)OCC + H2O']
            }
        }
    
    def validate_reaction(self, reactants: List[str], predicted_products: List[str], 
                         reaction_type: str = None) -> Dict:
        """
        Comprehensive validation of a predicted reaction
        
        Args:
            reactants: List of reactant SMILES
            predicted_products: List of predicted product SMILES
            reaction_type: Optional reaction type for template checking
            
        Returns:
            Validation report with all checks
        """
        validation_report = {
            'overall_valid': True,
            'confidence_score': 1.0,
            'issues': [],
            'checks': {}
        }
        
        # Check 1: SMILES validity
        smiles_check = self._validate_smiles(reactants + predicted_products)
        validation_report['checks']['smiles_validity'] = smiles_check
        if not smiles_check['all_valid']:
            validation_report['overall_valid'] = False
            validation_report['confidence_score'] *= 0.1
            validation_report['issues'].append("Invalid SMILES detected")
        
        # Check 2: Atom conservation
        atom_check = self._validate_atom_conservation(reactants, predicted_products)
        validation_report['checks']['atom_conservation'] = atom_check
        if not atom_check['conserved']:
            validation_report['overall_valid'] = False
            validation_report['confidence_score'] *= 0.2
            validation_report['issues'].append("Atom conservation violated")
        
        # Check 3: Valence validation
        valence_check = self._validate_valences(predicted_products)
        validation_report['checks']['valence_validity'] = valence_check
        if not valence_check['all_valid']:
            validation_report['confidence_score'] *= 0.5
            validation_report['issues'].append("Invalid valences detected")
        
        # Check 4: Reaction class consistency
        if reaction_type:
            class_check = self._validate_reaction_class(reactants, predicted_products, reaction_type)
            validation_report['checks']['reaction_class'] = class_check
            if not class_check['consistent']:
                validation_report['confidence_score'] *= 0.3
                validation_report['issues'].append("Inconsistent with reaction class")
        
        # Check 5: Energy reasonableness (basic heuristics)
        energy_check = self._validate_energy_reasonableness(reactants, predicted_products)
        validation_report['checks']['energy_reasonableness'] = energy_check
        if not energy_check['reasonable']:
            validation_report['confidence_score'] *= 0.7
            validation_report['issues'].append("Energetically unreasonable")
        
        # Check 6: Structural reasonableness
        structure_check = self._validate_structural_reasonableness(predicted_products)
        validation_report['checks']['structural_reasonableness'] = structure_check
        if not structure_check['reasonable']:
            validation_report['confidence_score'] *= 0.6
            validation_report['issues'].append("Structurally unreasonable")
        
        return validation_report
    
    def _validate_smiles(self, smiles_list: List[str]) -> Dict:
        """Validate that all SMILES are parseable by RDKit"""
        results = {
            'all_valid': True,
            'invalid_smiles': [],
            'valid_count': 0,
            'total_count': len(smiles_list)
        }
        
        for smiles in smiles_list:
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol is None:
                    results['all_valid'] = False
                    results['invalid_smiles'].append(smiles)
                else:
                    results['valid_count'] += 1
            except:
                results['all_valid'] = False
                results['invalid_smiles'].append(smiles)
        
        return results
    
    def _validate_atom_conservation(self, reactants: List[str], products: List[str]) -> Dict:
        """Check that atoms are conserved in the reaction"""
        def count_atoms(smiles_list):
            atom_count = Counter()
            for smiles in smiles_list:
                mol = Chem.MolFromSmiles(smiles)
                if mol:
                    for atom in mol.GetAtoms():
                        atom_count[atom.GetSymbol()] += 1
            return atom_count
        
        reactant_atoms = count_atoms(reactants)
        product_atoms = count_atoms(products)
        
        return {
            'conserved': reactant_atoms == product_atoms,
            'reactant_atoms': dict(reactant_atoms),
            'product_atoms': dict(product_atoms),
            'missing_atoms': dict((reactant_atoms - product_atoms)),
            'extra_atoms': dict((product_atoms - reactant_atoms))
        }
    
    def _validate_valences(self, smiles_list: List[str]) -> Dict:
        """Check that all atoms have reasonable valences"""
        results = {
            'all_valid': True,
            'valence_issues': []
        }
        
        for smiles in smiles_list:
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                try:
                    # Try to sanitize - this will catch valence errors
                    Chem.SanitizeMol(mol)
                except:
                    results['all_valid'] = False
                    results['valence_issues'].append(smiles)
        
        return results
    
    def _validate_reaction_class(self, reactants: List[str], products: List[str], 
                                reaction_type: str) -> Dict:
        """Check consistency with known reaction templates"""
        if reaction_type not in self.reaction_templates:
            return {'consistent': True, 'note': 'Unknown reaction type'}
        
        template = self.reaction_templates[reaction_type]
        
        # Basic pattern matching (simplified)
        if reaction_type == 'sn2':
            # Should have R-X + Nu -> R-Nu + X pattern
            has_halide = any('Br' in r or 'Cl' in r or 'I' in r for r in reactants)
            has_nucleophile = len(reactants) >= 2
            return {
                'consistent': has_halide and has_nucleophile,
                'template': template['pattern'],
                'checks': {'has_halide': has_halide, 'has_nucleophile': has_nucleophile}
            }
        
        elif reaction_type == 'diels_alder':
            # Should have diene + dienophile -> cyclic pattern
            return {
                'consistent': len(reactants) == 2 and len(products) >= 1,
                'template': template['pattern'],
                'note': 'Basic pattern check only'
            }
        
        return {'consistent': True, 'note': 'Template check not implemented'}
    
    def _validate_energy_reasonableness(self, reactants: List[str], products: List[str]) -> Dict:
        """Basic energy reasonableness checks using molecular complexity"""
        def complexity_score(smiles):
            mol = Chem.MolFromSmiles(smiles)
            if not mol:
                return 0
            return mol.GetNumAtoms() + mol.GetNumBonds() * 0.5
        
        reactant_complexity = sum(complexity_score(s) for s in reactants)
        product_complexity = sum(complexity_score(s) for s in products)
        
        # Very basic heuristic: complexity shouldn't change dramatically
        complexity_ratio = product_complexity / max(reactant_complexity, 1)
        
        return {
            'reasonable': 0.5 < complexity_ratio < 2.0,
            'reactant_complexity': reactant_complexity,
            'product_complexity': product_complexity,
            'ratio': complexity_ratio
        }
    
    def _validate_structural_reasonableness(self, products: List[str]) -> Dict:
        """Check for obviously unreasonable structures"""
        issues = []
        
        for smiles in products:
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                # Check for unreasonable ring sizes
                ring_info = mol.GetRingInfo()
                for ring in ring_info.AtomRings():
                    if len(ring) < 3 or len(ring) > 12:
                        issues.append(f"Unusual ring size {len(ring)} in {smiles}")
                
                # Check for too many heteroatoms
                hetero_count = sum(1 for atom in mol.GetAtoms() if atom.GetSymbol() not in ['C', 'H'])
                total_atoms = mol.GetNumAtoms()
                if hetero_count / max(total_atoms, 1) > 0.7:
                    issues.append(f"Too many heteroatoms in {smiles}")
        
        return {
            'reasonable': len(issues) == 0,
            'issues': issues
        }

def test_validation_system():
    """Test the validation system on our known reactions"""
    validator = ReactionValidator()
    
    print("🧪 TESTING REACTION VALIDATION SYSTEM")
    print("=" * 60)
    
    # Test 1: Diels-Alder (correct prediction)
    print("\n1. DIELS-ALDER REACTION (Correct)")
    reactants = ["C1=CC=CC1", "O=C1OC(=O)C=C1"]
    correct_products = ["O=C1OC(=O)C2C3CC(C3)CC12"]
    
    result = validator.validate_reaction(reactants, correct_products, 'diels_alder')
    print(f"   Valid: {result['overall_valid']}")
    print(f"   Confidence: {result['confidence_score']:.2f}")
    print(f"   Issues: {result['issues']}")
    
    # Test 2: Diels-Alder (AI hallucination)
    print("\n2. DIELS-ALDER REACTION (AI Hallucination)")
    wrong_products = ["O=C(O)c1cc(-n2c(=O)cc(C(F)(F)F)[nH]c2=O)ccc1Cl"]
    
    result = validator.validate_reaction(reactants, wrong_products, 'diels_alder')
    print(f"   Valid: {result['overall_valid']}")
    print(f"   Confidence: {result['confidence_score']:.2f}")
    print(f"   Issues: {result['issues']}")
    print(f"   Atom conservation: {result['checks']['atom_conservation']['conserved']}")
    
    # Test 3: SN2 Reaction (correct)
    print("\n3. SN2 REACTION (Correct)")
    sn2_reactants = ["CCCCBr", "C#N"]
    sn2_products = ["CCCCC#N", "[Br-]"]
    
    result = validator.validate_reaction(sn2_reactants, sn2_products, 'sn2')
    print(f"   Valid: {result['overall_valid']}")
    print(f"   Confidence: {result['confidence_score']:.2f}")
    print(f"   Issues: {result['issues']}")
    
    # Test 4: SN2 Reaction (AI hallucination)
    print("\n4. SN2 REACTION (AI Hallucination)")
    sn2_wrong = ["CC1(C)COCN1CC(C)(C)[N+](=O)[O-]"]
    
    result = validator.validate_reaction(sn2_reactants, sn2_wrong, 'sn2')
    print(f"   Valid: {result['overall_valid']}")
    print(f"   Confidence: {result['confidence_score']:.2f}")
    print(f"   Issues: {result['issues']}")
    
    print("\n" + "=" * 60)
    print("✅ VALIDATION SYSTEM TESTING COMPLETE")

if __name__ == "__main__":
    test_validation_system()
