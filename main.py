
"""
Main entry point for the Virtual Chemistry Lab.
Features a dual-engine system for DFT calculations or rapid surrogate screening.
"""
import argparse
import sys
import numpy as np

# --- Core, lightweight imports needed by both engines ---
from input_handler import smiles_to_ase
from product_predictor import predict_products
from surrogate_predictor import predict_energy_with_surrogate


def run_dft_simulation(args):
    """Encapsulates the original, high-accuracy DFT simulation workflow."""
    # --- DFT-SPECIFIC IMPORTS ---
    # These are imported ONLY when this function is called.
    print("Importing DFT modules...")
    from ase.io import write
    from collections import deque
    from molecule_optimizer import optimize_geometry, calculate_thermo_data
    from thermo_kinetics import check_thermodynamic_feasibility_G, calculate_gibbs_free_energy, calculate_activation_energy, calculate_arrhenius_rate_constant
    from reaction_pathway import setup_neb_calculation, run_neb
    from visualizer import plot_reaction_profile, plot_reaction_network
    from network_model import ReactionNetwork

    # Check if we have one reactant (single molecule analysis) or multiple (reaction analysis)
    if len(args.reactants) == 1:
        print(f"\n---🔬 Starting Single-Molecule DFT Analysis ---")
        print(f"Molecule: {args.reactants[0]}")

        # --- Single Molecule Analysis ---
        molecule_atoms = smiles_to_ase(args.reactants[0])
        if molecule_atoms is None:
            print(f"Error: Could not parse SMILES string: {args.reactants[0]}")
            return

        # Perform DFT calculation
        optimized_atoms = optimize_geometry(molecule_atoms, xc=args.xc, basis=args.basis, solvent=args.solvent)

        # Calculate thermochemical properties (with advanced properties if requested)
        advanced = getattr(args, 'advanced', False)
        temperature = getattr(args, 'temperature', 298.15)
        thermo_data = calculate_thermo_data(optimized_atoms, advanced=advanced, temperature=temperature, solvent=args.solvent)

        # Display results
        print(f"\n--- 🏁 DFT Analysis Complete ---")
        print(f"Molecule: {thermo_data['formula']}")
        print(f"Method: {thermo_data['method']}")
        if thermo_data['energy_hartree'] is not None:
            print(f"Total Energy: {thermo_data['energy_hartree']:.6f} Hartree")
            print(f"Total Energy: {thermo_data['energy_ev']:.4f} eV")
        print("-" * 40)

    else:
        # Multi-reactant analysis
        print(f"\n---🔬 Starting High-Accuracy DFT Reaction Analysis ---")
        print(f"Analyzing {len(args.reactants)} reactants: {', '.join(args.reactants)}")

        reactant_data = []
        total_reactant_energy = 0.0

        # Analyze each reactant
        for i, reactant_smiles in enumerate(args.reactants):
            print(f"\n--- Analyzing Reactant {i+1}: {reactant_smiles} ---")

            reactant_atoms = smiles_to_ase(reactant_smiles)
            if reactant_atoms is None:
                print(f"Error: Could not parse SMILES string: {reactant_smiles}")
                continue

            # Perform DFT calculation with advanced properties
            optimized_atoms = optimize_geometry(reactant_atoms, xc=args.xc, basis=args.basis, solvent=args.solvent)
            advanced = getattr(args, 'advanced', False)
            temperature = getattr(args, 'temperature', 298.15)
            thermo_data = calculate_thermo_data(optimized_atoms, advanced=advanced, temperature=temperature, solvent=args.solvent)

            reactant_data.append(thermo_data)
            if thermo_data['energy_hartree'] is not None:
                total_reactant_energy += thermo_data['energy_hartree']

        # Summary of all reactants
        print(f"\n--- 🏁 Multi-Reactant Analysis Complete ---")
        print("=" * 50)
        for i, data in enumerate(reactant_data):
            print(f"Reactant {i+1}: {data['formula']} ({args.reactants[i]})")
            if data['energy_hartree'] is not None:
                print(f"  Energy: {data['energy_hartree']:.6f} Hartree ({data['energy_ev']:.4f} eV)")

                # Show advanced properties if calculated
                if advanced and 'frontier_orbitals' in data:
                    fo = data['frontier_orbitals']
                    if fo and fo['homo_lumo_gap_ev']:
                        print(f"  HOMO-LUMO Gap: {fo['homo_lumo_gap_ev']:.3f} eV")
                        print(f"  Ionization Potential: {fo['ionization_potential_ev']:.3f} eV")

                if advanced and 'dipole_moment' in data:
                    dm = data['dipole_moment']
                    if dm and 'dipole_magnitude_debye' in dm:
                        print(f"  Dipole Moment: {dm['dipole_magnitude_debye']:.3f} Debye")

        print("=" * 50)
        print(f"Total Reactant Energy: {total_reactant_energy:.6f} Hartree ({total_reactant_energy * 27.2114:.4f} eV)")
        print(f"Method: {args.xc}/{args.basis}")

        # Perform reaction analysis if requested
        if getattr(args, 'reaction_analysis', False):
            print(f"\n🚀 Starting PROFESSIONAL Reaction Analysis...")
            print("   Using Advanced Reaction System with all features:")
            print("   ✅ Advanced Product Prediction")
            print("   ✅ Multiple Pathway Explorer")
            print("   ✅ Side Reaction Predictor")
            print("   ✅ Real Transition State Search")

            try:
                # Use the new Advanced Reaction System
                from advanced_reaction_system import AdvancedReactionSystem

                # Initialize the advanced system
                advanced_system = AdvancedReactionSystem(xc=args.xc, basis=args.basis)

                # Prepare reaction conditions if available
                reaction_conditions = {}
                if hasattr(args, 'temperature') and args.temperature:
                    reaction_conditions['temperature'] = args.temperature
                if hasattr(args, 'solvent') and args.solvent:
                    reaction_conditions['solvent'] = args.solvent
                if hasattr(args, 'catalyst') and args.catalyst:
                    reaction_conditions['catalyst'] = args.catalyst

                # Perform comprehensive analysis
                comprehensive_analysis = advanced_system.analyze_reaction_comprehensive(
                    reactant_smiles=args.reactants,
                    target_products=getattr(args, 'target_products', None),
                    reaction_conditions=reaction_conditions if reaction_conditions else None,
                    include_ts_search=getattr(args, 'include_ts_search', True)
                )

                # Export results if requested
                if getattr(args, 'export_results', False):
                    output_file = getattr(args, 'output_file', 'comprehensive_analysis.json')
                    advanced_system.export_comprehensive_analysis(comprehensive_analysis, output_file)

                # Legacy compatibility - extract best prediction for downstream use
                if comprehensive_analysis.product_predictions:
                    best_prediction = comprehensive_analysis.product_predictions[0]
                    predicted_products = best_prediction.get('products', [])

                    print(f"\n🎯 BEST PREDICTED REACTION (for legacy compatibility):")
                    print(f"Type: {best_prediction.get('reaction_type', 'unknown')}")
                    print(f"Products: {' + '.join(predicted_products)}")
                    print(f"Confidence: {best_prediction.get('confidence', 0.0):.2f}")
                else:
                    predicted_products = []

                if prediction_results['status'] == 'success' and prediction_results['best_prediction']:
                    best_prediction = prediction_results['best_prediction']
                    predicted_products = best_prediction['products']

                    print(f"\n🎯 Best Predicted Reaction:")
                    print(f"Type: {best_prediction['reaction_type']}")
                    print(f"Products: {' + '.join(predicted_products)}")
                    print(f"Confidence: {best_prediction['final_confidence']:.2f}")

                    # Step 2: DFT Analysis of Predicted Products
                    from reaction_analysis import ReactionAnalyzer

                    # Convert to ASE Atoms objects
                    reactant_atoms_list = []
                    for smiles in args.reactants:
                        atoms = smiles_to_ase(smiles)
                        if atoms:
                            reactant_atoms_list.append(atoms)

                    product_atoms_list = []
                    for smiles in predicted_products:
                        atoms = smiles_to_ase(smiles)
                        if atoms:
                            product_atoms_list.append(atoms)

                    if reactant_atoms_list and product_atoms_list:
                        analyzer = ReactionAnalyzer(xc=args.xc, basis=args.basis)

                        # Catalyst analysis if provided
                        catalyst_atoms = None
                        if args.catalyst:
                            catalyst_atoms = smiles_to_ase(args.catalyst)

                        # Perform complete reaction analysis with REAL predicted products
                        reaction_results = analyzer.complete_reaction_analysis(
                            reactants=reactant_atoms_list,
                            products=product_atoms_list,
                            catalyst=catalyst_atoms,
                            temperature=args.temperature
                        )

                        print("\n🎯 Quantum Chemical Analysis of Predicted Reaction:")
                        if 'thermodynamics' in reaction_results:
                            thermo = reaction_results['thermodynamics']
                            print(f"Reaction Energy: {thermo['reaction_energy_ev']:.3f} eV ({thermo['reaction_type']})")
                            print(f"Activation Barrier: {thermo['estimated_activation_barrier_ev']:.3f} eV")
                            print(f"Rate Assessment: {thermo['kinetic_assessment']}")
                            print(f"Feasibility: {thermo['thermodynamic_feasibility']}")

                        if 'catalyst_analysis' in reaction_results:
                            cat = reaction_results['catalyst_analysis']
                            print(f"Catalyst Effect: {cat['barrier_reduction_percent']:.1f}% barrier reduction")
                            print(f"Rate Enhancement: {cat['rate_enhancement_factor']:.2e}x")

                        # Store complete results
                        complete_results = {
                            'product_prediction': prediction_results,
                            'quantum_analysis': reaction_results,
                            'reactant_analysis': reactant_data
                        }

                        return complete_results

                else:
                    print("⚠️  No reliable product predictions found.")
                    print("Continuing with reactant analysis only.")

            except Exception as e:
                print(f"⚠️  Could not perform reaction analysis: {e}")
                print("Continuing with basic reactant analysis only.")

        # Store results for potential reaction analysis
        return reactant_data


def run_surrogate_screening(args):
    """Performs a rapid reaction screening using the GNN surrogate model."""
    print("\n---⚡ Starting Rapid Surrogate Screening ---")

    # --- 1. Reactant Setup ---
    reactant_atoms_list = [smiles_to_ase(s) for s in args.reactants]
    if any(atom is None for atom in reactant_atoms_list):
        print("Error: Could not parse reactant SMILES.", file=sys.stderr)
        return

    # --- 2. Predict Product ---
    predicted_products_smiles = predict_products(args.reactants, method='transformer')
    if not predicted_products_smiles:
        print("Could not predict a product. Halting screening.", file=sys.stderr)
        return

    product_smiles = predicted_products_smiles[0]
    product_atoms = smiles_to_ase(product_smiles)
    if product_atoms is None:
        print(f"Could not parse predicted product SMILES: {product_smiles}", file=sys.stderr)
        return

    # --- 3. Predict Energies with Surrogate ---
    print("\nCalculating reactant energies...")
    total_reactant_energy = 0.0
    for i, reactant_atoms in enumerate(reactant_atoms_list):
        energy = predict_energy_with_surrogate(reactant_atoms)
        if np.isnan(energy):
            print(f"Energy prediction failed for reactant {i+1}. Cannot determine reaction energy.", file=sys.stderr)
            return
        total_reactant_energy += energy

    print("\nCalculating product energy...")
    product_energy = predict_energy_with_surrogate(product_atoms)

    if np.isnan(product_energy):
        print("Energy prediction failed for product. Cannot determine reaction energy.", file=sys.stderr)
        return

    # --- 4. Calculate and Report Reaction Energy ---
    reaction_energy = product_energy - total_reactant_energy
    
    print("\n--- 🏁 Surrogate Screening Finished ---")
    print(f"Reactant(s): {', '.join(args.reactants)}")
    print(f"Predicted Product: {product_smiles}")
    print("-" * 30)
    print(f"Predicted Reaction Energy (ΔE₀): {reaction_energy:.4f} eV")
    print("-" * 30)
    if reaction_energy < 0:
        print("Result: The reaction is predicted to be EXOTHERMIC.")
    else:
        print("Result: The reaction is predicted to be ENDOTHERMIC.")

def main():
    """Main function to select the computational engine and run the appropriate task."""
    parser = argparse.ArgumentParser(
        description="Virtual Chemistry Lab - Dual Engine Simulator",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument('--engine', choices=['dft', 'surrogate'], default='dft',
                        help="Select the computational engine. 'dft' for high accuracy, 'surrogate' for rapid screening.")
    parser.add_argument('reactants', nargs='+', help="Initial SMILES strings of the reactant(s).")
    
    dft_group = parser.add_argument_group('DFT Engine Options')
    dft_group.add_argument('--xc', type=str, default='b3lyp', help="DFT exchange-correlation functional.")
    dft_group.add_argument('--basis', type=str, default='sto-3g', help="DFT basis set.")
    dft_group.add_argument('--solvent', type=str, default=None, help="Implicit solvent model (e.g., 'water', 'acetone', 'dmso').")
    dft_group.add_argument('--advanced', action='store_true', help="Calculate advanced properties (frequencies, charges, orbitals).")
    dft_group.add_argument('--temperature', type=float, default=298.15, help="Temperature in Kelvin for thermodynamic corrections.")
    dft_group.add_argument('--reaction-analysis', action='store_true', help="Perform ADVANCED reaction analysis with all professional features.")
    dft_group.add_argument('--catalyst', type=str, default=None, help="SMILES string for catalyst molecule.")

    # Advanced Analysis Options
    advanced_group = parser.add_argument_group('Advanced Analysis Options')
    advanced_group.add_argument('--target-products', nargs='*', help="Target product SMILES for pathway analysis.")
    advanced_group.add_argument('--include-ts-search', action='store_true', default=True, help="Include transition state search (computationally expensive).")
    advanced_group.add_argument('--no-ts-search', action='store_true', help="Skip transition state search for faster analysis.")
    advanced_group.add_argument('--max-pathways', type=int, default=5, help="Maximum number of pathways to explore.")
    advanced_group.add_argument('--max-steps', type=int, default=3, help="Maximum number of steps per pathway.")
    advanced_group.add_argument('--export-results', action='store_true', help="Export comprehensive results to JSON file.")
    advanced_group.add_argument('--output-file', type=str, default='comprehensive_analysis.json', help="Output file for exported results.")
    advanced_group.add_argument('--quick-check', action='store_true', help="Perform quick feasibility check only.")
    advanced_group.add_argument('--benchmark', action='store_true', help="Run system benchmark on test reactions.")

    # Analysis Mode Selection
    mode_group = parser.add_argument_group('Analysis Mode')
    mode_group.add_argument('--mode', choices=['ultimate', 'comprehensive', 'products-only', 'pathways-only', 'side-reactions-only', 'ts-only'],
                           default='comprehensive', help="Select specific analysis mode.")
    mode_group.add_argument('--ultimate-analysis', action='store_true', help="Use the ULTIMATE professional system with all features.")

    # Condition Specification
    conditions_group = parser.add_argument_group('Reaction Conditions')
    conditions_group.add_argument('--reaction-temperature', type=str, choices=['low', 'moderate', 'high'],
                                 help="Reaction temperature category.")
    conditions_group.add_argument('--reaction-solvent', type=str, choices=['polar_protic', 'polar_aprotic', 'nonpolar'],
                                 help="Solvent category for reaction.")
    conditions_group.add_argument('--reaction-atmosphere', type=str, choices=['air', 'inert', 'reducing'],
                                 help="Reaction atmosphere.")
    
    args = parser.parse_args()

    # Handle conflicting arguments
    if args.no_ts_search:
        args.include_ts_search = False

    print("--- 🧪 Welcome to the PROFESSIONAL Virtual Chemistry Lab 🧪 ---")
    print(f"Selected Engine: '{args.engine}'")

    # Handle special modes
    if args.engine == 'dft' and (args.ultimate_analysis or args.mode == 'ultimate'):
        print("\n🚀 ULTIMATE PROFESSIONAL ANALYSIS MODE")
        from ultimate_reaction_system import UltimateProfessionalReactionSystem, UltimateAnalysisRequest

        # Create ultimate system
        ultimate_system = UltimateProfessionalReactionSystem(dft_level=f"{args.xc}/{args.basis}")

        # Create analysis request
        request = UltimateAnalysisRequest(
            reactant_smiles=args.reactants,
            target_products=getattr(args, 'target_products', None),
            include_ts_search=args.include_ts_search,
            optimization_objectives=['yield', 'selectivity', 'cost', 'safety'],
            dft_level=f"{args.xc}/{args.basis}"
        )

        # Perform ultimate analysis
        result = ultimate_system.analyze_reaction_ultimate(request)

        # Export if requested
        if args.export_results:
            ultimate_system.export_ultimate_results(result, args.output_file)

        return

    if args.engine == 'dft' and args.quick_check:
        print("\n⚡ QUICK REACTION CHECK MODE")
        from advanced_reaction_system import AdvancedReactionSystem
        system = AdvancedReactionSystem(xc=args.xc, basis=args.basis)
        result = system.quick_reaction_check(args.reactants)
        return

    if args.engine == 'dft' and args.benchmark:
        print("\n🏃 BENCHMARK MODE")
        from advanced_reaction_system import AdvancedReactionSystem
        system = AdvancedReactionSystem(xc=args.xc, basis=args.basis)

        # Define test reactions for benchmarking
        test_reactions = [
            (['CCO', 'CC(=O)O'], ['CC(=O)OCC']),  # Esterification
            (['CC(=O)O', 'CCN'], ['CC(=O)NCC']),  # Amidation
            (['CCCl', 'O'], ['CCO']),             # SN2 substitution
        ]

        benchmark_results = system.benchmark_system_performance(test_reactions)
        return

    if args.engine == 'dft':
        run_dft_simulation(args)
    elif args.engine == 'surrogate':
        run_surrogate_screening(args)

if __name__ == "__main__":
    main()